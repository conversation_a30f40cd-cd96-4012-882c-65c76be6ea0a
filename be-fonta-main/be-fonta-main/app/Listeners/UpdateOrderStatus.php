<?php

namespace App\Listeners;

use App\Events\PaymentEvent;
use App\Models\MassDrop;
use App\Models\MassDropUser;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Transaction;
use Illuminate\Contracts\Queue\ShouldQueue;

class UpdateOrderStatus implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PaymentEvent $event): void
    {
        info('UpdateOrderStatus.handle');

        // Check if transaction exists by order_id from params
        // when payment status is settlement, update transaction status to paid
        // Decrement product stock
        // Update order status to paid

        $event->transaction->status = $event->payment['transaction_status'];

        if ($event->payment['transaction_status'] === 'settlement') {

            $event->transaction->status = Transaction::STATUS_PAID;

            $event->transaction->paid_at = now();

            $event->transaction->order->update([
                'status'         => Order::STATUS_PAID,
                'payment_method' => $event->payment['payment_type'],
            ]);

            $orderItems = OrderDetail::with(['product'])
                ->where('order_id', $event->transaction->order_id)
                ->get();

            // Todo: Check if stock is enough
            foreach ($orderItems as $orderItem) {
                if ($orderItem->product) {
                    $orderItem->product->decrement('stock', $orderItem->quantity);
                }
            }

            // check if massDrop
            if ($event->transaction->order->mass_drop_id) {
                // update count order mass Drop
                $massDrop = MassDrop::where('id', $event->transaction->order->mass_drop_id)->first();
                $massDropUser = MassDropUser::where('id', $event->transaction->order->mass_drop_user_id)
                    ->select('id', 'quantity')
                    ->first();

                if ($massDrop) {
                    $massDrop->increment('count_orders', $massDropUser->quantity);
                }
            }

            $event->transaction->save();

            return;
        }

        if ($event->payment['transaction_status'] === 'expire') {
            $event->transaction->order->update([
                'status'         => $event->payment['transaction_status'],
                'payment_method' => $event->payment['payment_type'],
            ]);
        }
    }
}
