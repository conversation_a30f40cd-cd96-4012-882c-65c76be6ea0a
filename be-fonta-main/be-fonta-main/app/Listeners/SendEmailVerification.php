<?php

namespace App\Listeners;

use App\Enums\Provider;
use App\Events\UserRegisterEvent;
use App\Jobs\EmailVerificationJob;
use App\Mail\EmailVerification;
use App\Models\VerificationToken;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendEmailVerification implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegisterEvent $event): void
    {
        info('Fire event UserRegisterEvent');
        if ($event->provider === Provider::EMAIL) {
            // $token = bin2hex(random_bytes(32));

            // VerificationToken::create([
            //     'identity' => $event->identity,
            //     'token' => $token,
            // ]);

            // generate OTP
            $codeOtp = rand(100000, 999999);

            VerificationToken::create([
                'identity' => $event->identity,
                'token'    => $codeOtp,
            ]);

            $params = [
                'name' => $event->user->name ?? 'Fontawesome',
                'otp' => $codeOtp,
            ];

            EmailVerificationJob::dispatch($event->identity, $params);
        }
    }
}
