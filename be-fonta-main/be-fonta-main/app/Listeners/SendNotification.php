<?php

namespace App\Listeners;

use App\Events\NotificationEvent;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NotificationEvent $event): void
    {
        info('SendNotification.handle', [
            'userId' => $event->userId,
            'title' => $event->title,
            'body' => $event->body,
            'data' => $event->data,
            'type' => $event->type,
        ]);

        app(NotificationService::class)->sendByUserId(
            userId: $event->userId,
            title: $event->title,
            body: $event->body,
            data: $event->data,
            type: $event->type ?? 'info',
        );
    }
}
