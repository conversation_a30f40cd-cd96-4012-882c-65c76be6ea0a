<?php

namespace App\Listeners;

use App\Events\RefundOrderEvent;
use App\Models\Order;
use App\Models\UserBalance;
use App\Models\UserBalanceLog;

class ReducePoint
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(RefundOrderEvent $event): void
    {
        info('ReducePoint.handle', [
            'order_id' => $event->order->id,
        ]);

        // reduce point
        $userBalance = UserBalance::where('user_id', $event->order->user_id)->first();

        if ($userBalance) {
            // check if Order has ben refunded
            $alreadyRefunded = UserBalanceLog::where('loggable_id', $event->order->id)
                ->where('balance_type', 'point')
                ->where('type', 'out')
                ->exists();

            if ($alreadyRefunded) {
                info('ReducePoint.handle: already refunded', [
                    'order_id' => $event->order->id,
                ]);

                return;
            }


            $newPoint = $userBalance->point - floor($event->order->sub_total / config('option.conversion_rate'));

            $userBalance->logs()->create([
                'user_id'       => $event->order->user_id,
                'amount'        => abs($newPoint - $userBalance->point),
                'new_amount'    => $newPoint,
                'old_amount'    => $userBalance->point,
                'type'          => 'out',
                'balance_type'  => 'point',
                'loggable_id'   => $event->order->id,
                'loggable_type' => Order::class,
            ]);

            $userBalance->update([
                'point' => $newPoint,
            ]);
        }
    }
}
