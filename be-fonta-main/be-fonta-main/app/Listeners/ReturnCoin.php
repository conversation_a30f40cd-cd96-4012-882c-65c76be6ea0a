<?php

namespace App\Listeners;

use App\Events\CancelOrderEvent;
use App\Models\Order;
use App\Models\Transaction;
use App\Models\UserBalance;
use Illuminate\Contracts\Queue\ShouldQueue;

class ReturnCoin implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CancelOrderEvent $event): void
    {
        info('ReturnCoin.handle');

        $order = $event->order;

        // check if order has transaction status paid
        $orderHasPaid = $order->transactions
            ->where('status', Transaction::STATUS_PAID)
            ->first();

        if ($orderHasPaid) {
            info('Order has paid transaction', [
                'order_id' => $order->id,
            ]);
            // return coin to user balance
            $userBalance = UserBalance::where('user_id', $order->user_id)
                ->first();

            if ($userBalance) {
                $userBalance->logs()->create([
                    'user_id'       => $order->user_id,
                    'amount'        => $order->total,
                    'new_amount'    => $userBalance->coin + $order->total,
                    'old_amount'    => $userBalance->coin,
                    'type'          => 'in',
                    'balance_type'  => 'coin',
                    'loggable_id'   => $order->id,
                    'loggable_type' => Order::class,
                ]);

                $userBalance->update([
                    'coin' => $userBalance->coin + $order->total,
                ]);

                info('Coins returned successfully', [
                    'user_id'  => $order->user_id,
                    'coins'    => $order->total,
                    'order_id' => $order->id,
                ]);
            }

            return;
        }

        info('Order has no paid transaction', [
            'order_id' => $order->id,
        ]);
    }
}
