<?php

namespace App\Listeners;

use App\Events\UserRegisterEvent;
use App\Models\ReferralUsage;
use App\Models\User;
use App\Models\UserBalance;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class InitialBalance implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegisterEvent $event): void
    {
        info('InitialBalance.handle');

        UserBalance::create([
            'user_id' => $event->user->id,
        ]);

        // check if referral code is not null
        if ($event->referralCode !== null) {

            // check referral code
            $userReferrer = User::where('referral_code', $event->referralCode)
                ->select('id', 'referral_code')
                ->first();

            if ($userReferrer) {
                info('AddPoint.toReferrer');

                $balance = UserBalance::where('user_id', $userReferrer->id)
                    ->first();

                if ($balance) {
                    $balance->point += 1000;
                    $balance->save();

                    ReferralUsage::create([
                        'referrer_id'   => $userReferrer->id,
                        'used_by'       => $event->user->id,
                        'referral_code' => $event->referralCode,
                        'point'        => 1000,
                        'description'   => 'Referral bonus',
                        'type'          => ReferralUsage::TYPE_REGISTER,
                    ]);
                }
            }
        }
    }
}
