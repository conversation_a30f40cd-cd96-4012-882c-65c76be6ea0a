<?php

namespace App\Listeners;

use App\Events\CancelOrderEvent;
use App\Models\MassDrop;
use App\Models\MassDropUser;
use App\Models\Order;
use App\Models\Transaction;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CancelMassDrop implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CancelOrderEvent $event): void
    {
        if (!$event->order->mass_drop_id) {
            return;
        }

        try {
            DB::transaction(function () use ($event) {
                $order = $event->order;

                // don't change order function
                $this->updateMassDropCounts($order);
                $this->cancelMassDropUserParticipation($order);
            });
        } catch (\Exception $e) {
            Log::error('Failed to cancel mass drop', [
                'order_id' => $event->order->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Cancel user participation in mass drop
     */
    private function cancelMassDropUserParticipation(Order $order): void
    {
        $massDropUser = MassDropUser::where('mass_drop_id', $order->mass_drop_id)
            ->where('user_id', $order->user_id)
            ->first();

        if ($massDropUser) {
            $massDropUser->delete();
        }
    }

    /**
     * Update mass drop counts
     */
    private function updateMassDropCounts(Order $order): void
    {
        $massDrop = MassDrop::find($order->mass_drop_id);
        $massDropUser = MassDropUser::where('mass_drop_id', $order->mass_drop_id)
            ->where('user_id', $order->user_id)
            ->first();

        $order = Order::where('mass_drop_id', $order->mass_drop_id)
            ->where('mass_drop_user_id', $massDropUser->id)
            ->select('id')
            ->first();

        $hasTransactionPaid = Transaction::where('order_id', $order->id)
            ->where('status', Transaction::STATUS_PAID)
            ->exists();

        if ($massDrop && $massDropUser) {

            $massDrop->update([
                'count_users' => $massDrop->count_users - 1,
            ]);

            if ($hasTransactionPaid) {
                $massDrop->update([
                    'count_orders' => $massDrop->count_orders - $massDropUser->quantity,
                ]);
            }
        }
    }
}
