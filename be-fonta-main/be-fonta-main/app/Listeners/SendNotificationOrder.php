<?php

namespace App\Listeners;

use App\Enums\Screen;
use App\Enums\NotificationType;
use App\Events\PaymentEvent;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendNotificationOrder
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PaymentEvent $event): void
    {
        info('SendNotificationOrder.handle');

        if ($event->payment['transaction_status'] === 'settlement') {
            // Send notification to user
            app(NotificationService::class)->sendByUserId(
                userId: $event->transaction->order->user_id,
                title: '<PERSON><PERSON>an Anda telah dibayar',
                body: 'Pesanan dengan Invoice ' .  $event->transaction->order->invoice_number . ' telah dibayar, pesanan akan segera diproses.',
                data: [
                    'screen' => Screen::TRANSACTION_DETAIL->value,
                    'id'     => $event->transaction->order_id,
                ],
                type: NotificationType::INFO->value,
            );

            // Send notification to admin
        }
    }
}
