<?php

namespace App\Listeners;

use App\Enums\Provider;
use App\Events\UserRegisterEvent;
use App\Jobs\MobilePhoneVerificationJob;
use App\Models\VerificationToken;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendPhoneVerification implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegisterEvent $event): void
    {
        info('Fire event UserRegisterEvent');

        if ($event->provider === Provider::PHONE) {
            // generate OTP
            $codeOtp = rand(100000, 999999);

            VerificationToken::create([
                'identity' => $event->identity,
                'token'    => $codeOtp,
            ]);

            // dispatch job
            dispatch(new MobilePhoneVerificationJob($event->identity, [
                'is_whatsapp' => $event->user->is_whatsapp,
                'message'     => $codeOtp . ' adalah kode verifikasi akun Fontawesome Anda. Jangan berikan kode ini kepada siapapun.',
                'otp'         => $codeOtp,
            ]));
        }
    }
}
