<?php

namespace App\Services;

use App\Services\Impl\VerifyImplService;
use Illuminate\Support\Facades\Http;

class WhatsappService
{
    public function sendOtp(string $phone, array $params): void
    {
        info('WhatsappService.sendOtp');

        if (config('whatsapp.is_active') === false) {
            app(VerifyImplService::class)->sendOtpByWhatsApp($phone, $params);

            return;
        }

        $paramRequest = $this->getTemplateMessage('send_otp', [
            'to'        => $phone,
            'lang'      => $params['lang'] ?? 'en_US',
            'components' => [
                [
                    'type'       => 'body',
                    'parameters' => [
                        [
                            'type' => 'text',
                            'text' => (string)$params['otp'],
                        ],
                    ],
                ],
                [
                    'type'       => 'button',
                    'sub_type'   => 'url',
                    'index'      => 0,
                    'parameters' => [
                        [
                            'type' => 'text',
                            'text' => (string)$params['otp'],
                        ],
                    ],
                ],
            ],
        ]);

        $this->sendMessageTemplate($phone, $paramRequest);
    }

    public function sendMessage(string $phone, string $message): void
    {
        info('WhatsappService.sendMessage');
        info('Send message to phone ' . $phone);
        info('Message: ' . $message);
    }

    public function sendMessageTemplate(string $to, array $params)
    {
        info('WhatsappService.sendTemplate');
        info('Send template to phone ' . $to);
        info('Params: ' . json_encode($params));

        $response = Http::timeout(160)
            ->withHeaders([
                'Content-Type'  => 'application/json',
                'Authorization' => 'Bearer ' . config('whatsapp.access_token'),
            ])->post(
                config('whatsapp.base_url') . config('whatsapp.phone_number') . config('whatsapp.urls.send_message'),
                $params,
            );

        if ($response->successful()) {
            return $response->json();
        }

        throw new \Exception('Failed to send WhatsApp message: ' . $response->body());
    }

    private function getTemplateMessage(string $template, array $params): array
    {
        return [
            'messaging_product' => 'whatsapp',
            'to'                => $params['to'],
            'type'              => 'template',
            'template'          => [
                'name'       => $template,
                'language'   => [
                    'code' => $params['lang'] ?? 'en_US',
                ],
                'components' => $params['components'] ?? [],
            ],
        ];
    }
}
