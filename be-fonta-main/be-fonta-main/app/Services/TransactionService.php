<?php

namespace App\Services;

use App\Events\CancelOrderEvent;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Number;
use Spatie\Browsershot\Browsershot;
use Spatie\LaravelPdf\Facades\Pdf;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class TransactionService
{
    public function getTransactions($params)
    {
        info('TransactionService.getTransactions');

        return Order::with(['orderItems.product.images', 'orderItems.productVariant', 'transactions'])
            ->where('user_id', $params['user_id'])
            ->when(isset($params['status']), function ($query) use ($params) {
                return $query->where('status', $params['status']);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($params['per_page'] ?? 15);
    }

    public function checkStatusPayment($transactionId)
    {
        return app(PaymentService::class)->checkStatusPayment($transactionId);
    }

    public function generateInvoice($transactionId)
    {

        $order = Order::with([
            'transactions' => function ($query) {
                $query->orderBy('created_at', 'desc')
                    ->take(1);
            },
        ])
            ->where('id', $transactionId)
            ->first();

        if (! $order) {
            throw new BadRequestHttpException('Transaction not found.');
        }

        if (! $order->invoice_number) {
            throw new BadRequestHttpException('Invoice number not found.');
        }

        $invoice = $this->getOrder($order->invoice_number);

        $params = [
            'invoice_number' => $invoice->invoice_number,
            'buyer_name'     => $invoice->user->name ?? '',
            'buyer_date'     => $invoice->created_at->format('d F Y'),
            'buyer_address'  => $invoice->address->address ?? '',
            'items'          => $invoice->orderItems->map(function ($item) {
                return [
                    'name'     => $item->product->name,
                    'quantity' => $item->quantity,
                    'price'    => Number::currency($item->price, 'IDR', 'id_ID'),
                    'total'    => Number::currency($item->price * $item->quantity, 'IDR', 'id_ID'),
                ];
            }),
            'shipping_cost'  => Number::currency($invoice->shipping_cost, 'IDR', 'id_ID'),
            'discount'       => Number::currency($invoice->discount_total, 'IDR', 'id_ID'),
            'sub_total'      => Number::currency($invoice->sub_total, 'IDR', 'id_ID'),
            'tax_total'      => Number::currency($invoice->tax_total, 'IDR', 'id_ID'),
            'total'          => Number::currency($invoice->total, 'IDR', 'id_ID'),
            'payment_method' => $invoice->transactions->first()->payment_type ?? '',
            'last_update'    => $invoice->updated_at->format('d F Y H:i:s'),
            'status'         => $invoice->transactions->first()->status ?? '',
        ];

        if (! $invoice) {
            throw new BadRequestHttpException('Invoice not found.');
        }

        return Pdf::view('pdf.invoice', $params)
            ->name('invoice-' . $invoice->invoice_number . '.pdf')
            ->withBrowsershot(function (Browsershot $browsershot) {
                $browsershot
                    ->noSandbox()
                    ->setNodeBinary(config('option.path.node_binary'))
                    ->setNpmBinary(config('option.path.npm_binary'));
            })
            ->download();
    }

    private function getOrder($invoiceNumber)
    {
        return Order::with(['orderItems.product', 'user', 'address', 'transactions' => function ($query) {
            // $query->where('status', Transaction::STATUS_PAID)
            $query->orderBy('created_at', 'desc')
                ->limit(1);
        }])
            ->where('invoice_number', $invoiceNumber)
            ->where('invoice_number', '!=', null)
            ->first();
    }

    public function cancelTransaction($params, $orderId)
    {
        $order = Order::with(['transactions'])
            ->findOrFail($orderId);

        if (! $order) {
            throw new BadRequestHttpException('Transaction not found.');
        }

        if ($order->user_id !== $params['user_id']) {
            throw new BadRequestHttpException('Transaction not found.');
        }

        if (!in_array($order->status, Order::CANCELLABLE_STATUSES)) {
            throw new BadRequestHttpException('Transaction cannot be cancelled.');
        }

        $order->update([
            'status' => Order::STATUS_CANCELLED
        ]);

        event(new CancelOrderEvent($order));

        return $order;
    }

    public function refund($params, $orderId)
    {
        $transaction = Order::with(['transactions'])
            ->where('id', $orderId)
            ->first();

        if (! $transaction) {
            throw new BadRequestHttpException('Transaction not found.');
        }

        if ($transaction->user_id !== $params['user_id']) {
            throw new BadRequestHttpException('Transaction not found.');
        }

        if (!in_array($transaction->status, Order::REFUNDABLE_STATUSES)) {
            throw new BadRequestHttpException('Transaction cannot be refunded.');
        }

        $proofs = [];

        // upload proof
        foreach ($params['proofs'] as $proof) {
            $filePath = uploadFile($proof, 'refund_proofs'); //
            $proofs[] = [
                'file_path' => $filePath,
                'file_name' => $proof->getClientOriginalName(),
                'file_size' => $proof->getSize(), // in bytes  to KB
                'file_type' => $proof->getClientMimeType(),
            ];
        }

        DB::beginTransaction();

        try {
            $newRefund = Refund::create([
                'notes'    => $params['notes'],
                'order_id' => $transaction->id,
            ]);

            $newRefund->proofs()->createMany($proofs);

            // add log
            activity(Order::EVENT_STATUS)
                ->causedBy(auth('api')->user())
                ->on($transaction)
                ->event('updated')
                ->log('Refund requested');

            DB::commit();

            return $newRefund->load(['order', 'proofs', 'order.user', 'order.transactions']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BadRequestHttpException('Failed to refund transaction.');
        }

        return $transaction;
    }

    public function refundAction($params)
    {
        $refund = Refund::with(['order'])
            ->findOrFail($params['refund_id']);

        if (! $refund) {
            throw new BadRequestHttpException('Refund not found.');
        }

        if ($params['status'] === Refund::STATUS_APPROVED) {
            $refund->order->update([
                'status' => Order::STATUS_REFUNDED
            ]);

            return $refund->update([
                'status' => $params['status']
            ]);
        } else {
            $refund->update([
                'status' => $params['status']
            ]);

            activity(Order::EVENT_STATUS)
                ->causedBy(auth('web')->user())
                ->on($refund->order)
                ->event('updated')
                ->log('Refund ' . $params['status']);
        }

        return $refund;
    }

    public function getTransactionDetail($params, $transactionId)
    {
        return Order::with(['orderItems.product.images', 'orderItems.productVariant', 'transactions', 'shipping', 'address' => function ($query) {
            $query->select([
                'name',
                'address',
                'postal_code',
                'lat_lng',
                'is_default',
                'note',
                'name_receiver',
                'phone_receiver',
                'city_id',
                'province_id',
                'city',
                'type', // kota, kabupaten
                'province',
                'id',
            ]);
        }])
            ->where('user_id', $params['user_id'])
            ->findOrFail($transactionId);
    }

    public function done($params, $transactionId)
    {
        $order = Order::with(['transactions'])
            ->findOrFail($transactionId);

        if (! $order) {
            throw new BadRequestHttpException('Transaction not found.');
        }

        if ($order->user_id !== $params['user_id']) {
            throw new BadRequestHttpException('Transaction not found.');
        }

        if (!in_array($order->status, Order::COMPLETABLE_STATUSES)) {
            throw new BadRequestHttpException('Transaction cannot be marked as done.');
        }

        $order->update([
            'status' => Order::STATUS_DONE
        ]);

        return true;
    }
}
