<?php

namespace App\Services\Impl;

use App\Services\VerifyService;
use Illuminate\Support\Facades\Http;

class VerifyImplService implements VerifyService
{
    public function sendOtpByWhatsApp($to, $params)
    {
        info('VerifyImplService.sendOtpByWhatsApp');

        $params = [
            'userkey' => config('zenziva.user'),
            'passkey' => config('zenziva.password'),
            'to' => $to,
            'message' => $params['message'],
        ];

        $response = Http::timeout(160)->withHeaders([
            'Content-Type'  => 'application/json',
        ])->post(config('zenziva.base_url') . config('zenziva.urls.wa_regular'), $params);

        return $response->json();
    }

    public function bySMS($to, $params)
    {
        //TODO: Implement bySMS() method.
    }
}
