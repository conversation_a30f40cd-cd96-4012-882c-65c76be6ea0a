<?php

namespace App\Services;

use App\Jobs\SendReminderJob;
use App\Models\Reminder;

class ReminderService
{
    public function createReminder($params)
    {
        info('ReminderService.createReminder');

        $reminder = Reminder::create([
            'title'             => $params['title'],
            'description'       => $params['description'],
            'type'              => $params['type'], // push, email, sms
            'status'            => $params['status'],
            'reminder_at'       => $params['reminder_at'],
            'user_id'           => $params['user_id'],
            'reminder_interval' => $params['reminder_interval'],
            'subject_id'        => $params['subject_id'],
            'subject_type'      => $params['subject_type'],
        ]);

        if ($reminder->reminder_interval === Reminder::REMINDER_INTERVAL_NONE) {
            // dispatch job
            // $delay = $reminder->reminder_at->diffInSeconds(now());
            $delay = now()->diffInSeconds($reminder->reminder_at);

            SendReminderJob::dispatch($reminder)
                ->delay($delay);
        }

        return $reminder;
    }
}
