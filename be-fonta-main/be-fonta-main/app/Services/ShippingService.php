<?php

namespace App\Services;

use App\Models\UserAddress;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class ShippingService
{
    public function checkShippingCost($origin, $destination, $weight, $courier)
    {
        info('ShippingService.checkShippingCost');

        // check if the results are already cached
        if (Cache::has('shipping_cost_' . $origin . '_' . $destination . '_' . $weight . '_' . $courier)) {
            return Cache::get('shipping_cost_' . $origin . '_' . $destination . '_' . $weight . '_' . $courier);
        }

        $response = Http::withHeaders([
            'key' => config('shipping.providers.raja_ongkir.api_key'),
        ])->post(config('shipping.providers.raja_ongkir.base_url') . '/cost', [
            'origin'      => $origin,
            'destination' => $destination,
            'weight'      => $weight,
            'courier'     => $courier,
        ]);

        if ($response->successful()) {
            $results = $response->json();

            // cache the results
            // user query params as cache key
            // cache the results for 1 hour
            Cache::put('shipping_cost_' . $origin . '_' . $destination . '_' . $weight . '_' . $courier, $results['rajaongkir']['results'], 60 * 60);

            return $results['rajaongkir']['results'];
        }

        return null;
    }

    public function getProvinces($provinceId = null)
    {
        info('ShippingService.getProvinces');

        // query builder
        $queryParams = [
            'id' => $provinceId,
        ];

        // check if the results are already cached
        if (Cache::has('provinces_' . $provinceId)) {
            return Cache::get('provinces_' . $provinceId);
        }

        $response = Http::withHeaders([
            'key' => config('shipping.providers.raja_ongkir.api_key'),
        ])->get(config('shipping.providers.raja_ongkir.base_url') . '/province' . '?' . http_build_query($queryParams));

        if ($response->successful()) {
            $results = $response->json();

            // cache the results
            // user query params as cache key
            // cache the results for 1 hour
            Cache::put('provinces_' . $provinceId, $results['rajaongkir']['results'], 60 * 60);

            return $results['rajaongkir']['results'];
        }

        return null;
    }

    public function getCities($cityId = null, $provinceId = null, $name = null)
    {
        info('ShippingService.getCities');

        // check if the results are already cached
        if (Cache::has('cities_')) {
            $cities = Cache::get('cities_');

            if ($cityId) {
                return collect($cities)->filter(function ($city) use ($cityId) {
                    return $city['city_id'] == $cityId;
                })->values();
            }

            if ($provinceId || $name) {
                return collect($cities)->filter(function ($city) use ($provinceId, $name) {
                    if ($provinceId && $name) {
                        return $city['province_id'] == $provinceId && str_contains(strtolower($city['city_name']), strtolower($name));
                    }

                    if ($provinceId) {
                        return $city['province_id'] == $provinceId;
                    }

                    return str_contains(strtolower($city['city_name']), strtolower($name));
                })->values();
            }

            return collect($cities)->values();
        }

        $response = Http::withHeaders([
            'key' => config('shipping.providers.raja_ongkir.api_key'),
        ])->get(config('shipping.providers.raja_ongkir.base_url') . '/city');

        if ($response->successful()) {
            $results = $response->json();

            // cache the results
            // user query params as cache key
            // cache the results for 1 hour
            Cache::put('cities_', $results['rajaongkir']['results'], 60 * 60);

            $cities = $results['rajaongkir']['results'];

            if ($cityId) {
                return collect($cities)->filter(function ($city) use ($cityId) {
                    return $city['city_id'] == $cityId;
                })->values();
            }

            if ($provinceId || $name) {
                return collect($cities)->filter(function ($city) use ($provinceId, $name) {
                    if ($provinceId && $name) {
                        return $city['province_id'] == $provinceId && str_contains(strtolower($city['city_name']), strtolower($name));
                    }

                    if ($provinceId) {
                        return $city['province_id'] == $provinceId;
                    }

                    return str_contains(strtolower($city['city_name']), strtolower($name));
                })->values();
            }

            return collect($cities)->values();
        }

        return null;
    }

    public function getCityByPostalCode($postalCode)
    {
        info('ShippingService.getCityByPostalCode');

        // check if the results are already cached
        if (Cache::has('city_by_postal_code_' . $postalCode)) {
            return Cache::get('city_by_postal_code_' . $postalCode);
        }

        $cities = $this->getCities(null);

        if ($cities) {
            $city = collect($cities)->first(function ($city) use ($postalCode) {
                return $city['postal_code'] === $postalCode;
            });

            // cache the results
            // user query params as cache key
            // cache the results for 1 hour
            Cache::put('city_by_postal_code_' . $postalCode, $city, 60 * 60);

            return $city;
        }
    }

    public function checkCostByPostalCode($postalCode, $weight, $courier)
    {
        info('ShippingService.checkCostByPostalCode');

        $city = $this->getCityByPostalCode($postalCode);

        if ($city) {
            return $this->checkShippingCost(
                config('shipping.default_origin'),
                $city['city_id'],
                $weight,
                $courier
            );
        }
    }

    public function checkCostByAddress($params)
    {
        info('ShippingService.checkCostByAddress');

        $address = UserAddress::findOrFail($params['address_id']);

        return $this->checkShippingCost(
            config('shipping.default_origin'),
            $address->city_id,
            $params['weight'],
            $params['courier'],
        );
    }
}
