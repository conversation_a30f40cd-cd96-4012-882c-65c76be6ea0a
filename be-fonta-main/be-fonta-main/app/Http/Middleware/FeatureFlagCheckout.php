<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class FeatureFlagCheckout
{
    public function handle(Request $request, Closure $next): Response
    {
        if (config('option.can_checkout') === false) {
            throw new BadRequestHttpException('Feature transactions is disabled, please contact support.');
        }

        return $next($request);
    }
}
