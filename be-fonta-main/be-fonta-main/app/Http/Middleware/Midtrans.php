<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Midtrans
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $strings = $request->order_id . $request->status_code . $request->gross_amount . config('midtrans.server_key');

        if (hash('sha512', $strings) !== $request->signature_key) {
            abort(403, 'Unauthorized action.');
        }

        return $next($request);
    }
}
