<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class LogExecutionTime
{
    const DATE_FORMAT = 'Y-m-d H:i:s.u';

    public static $hiddenRequestParameters = [
        'password',
        'password_confirmation',
    ];

    public static $hiddenRequestHeaders = [
        'cookie',
        'x-csrf-token',
        'x-xsrf-token',
        'authorization',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Start calculating execution time
        $start_time = microtime(true);

        // Process the request
        $response = $next($request);

        // Calculate the execution time
        $end_time = microtime(true);

        $execution_time = $end_time - $start_time;
        $request_name = $request->path();
        $exploded_path = explode('/', $request_name);

        // Store the information to the logs
        $category = match ($exploded_path[0]) {
            'admin' => 'admin',
            'v1' => 'api',
            default => 'platform'
        };

        $code = $response->getStatusCode();

        // Filter sensitive request parameters
        $filteredPayload = collect($request->all())->map(function ($value, $key) {
            return in_array($key, self::$hiddenRequestParameters) ? '******' : $value;
        })->toArray();

        // Filter sensitive headers
        $filteredHeaders = collect($request->headers->all())->map(function ($value, $key) {
            return in_array($key, self::$hiddenRequestHeaders) ? '******' : $value;
        })->toArray();

        if (in_array(
            $code,
            [200, 201]
        )) {
            Log::channel('activity')->info('execution_time', [
                'code'       => $response->getStatusCode() ?? null,
                'response'   => null,
                'path'       => $request_name,
                'headers'    => $filteredHeaders ?? null,
                'payload'    => $filteredPayload ?? null,
                'start_time' => microTimeToFormat($start_time),
                'end_time'   => microTimeToFormat($end_time),
                'value'      => $execution_time,
                'category'   => $category,
            ]);
        } else {
            Log::channel('activity')->info('execution_time', [
                'code'       => $response->getStatusCode() ?? null,
                'response'   => json_decode($response->content(), true) ?? null,
                'path'       => $request_name,
                'headers'    => $filteredHeaders ?? null,
                'payload'    => $filteredPayload ?? null,
                'start_time' => microTimeToFormat($start_time),
                'end_time'   => microTimeToFormat($end_time),
                'value'      => $execution_time,
                'category'   => $category,
            ]);
        }

        return $response;
    }
}
