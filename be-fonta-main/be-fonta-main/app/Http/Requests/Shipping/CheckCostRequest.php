<?php

namespace App\Http\Requests\Shipping;

use Illuminate\Foundation\Http\FormRequest;

class CheckCostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'     => ['required', 'string'],
            // 'origin'      => ['required', 'string'],
            // 'destination' => ['required', 'string'],
            'address_id'  => ['required', 'string'],
            'weight'      => ['required', 'numeric', 'min:0'],
            'courier'     => ['nullable', 'string', 'in:jne,pos,tiki'],
        ];
    }
}
