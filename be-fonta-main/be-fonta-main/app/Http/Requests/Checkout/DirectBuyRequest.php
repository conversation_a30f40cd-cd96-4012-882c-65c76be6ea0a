<?php

namespace App\Http\Requests\Checkout;

use Illuminate\Foundation\Http\FormRequest;

class DirectBuyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'    => ['required', 'string'],
            'product_id' => ['required', 'string'],
            'variant_id' => ['nullable', 'string'],
            'quantity'   => ['required', 'integer'],
            'address_id' => ['required', 'string'],
            // 'payment_method' => ['required', 'string'],
            'payment_type'    => ['required', 'string'],
            'discount_code'   => ['nullable', 'string'],
            'courier'         => ['required', 'string', 'in:jne,pos,tiki,fonta'],
            'courier_service' => ['required_if:courier,jne,pos,tiki', 'string'],
            'notes'           => ['nullable', 'string'],
        ];
    }
}
