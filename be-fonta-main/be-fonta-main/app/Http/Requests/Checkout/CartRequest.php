<?php

namespace App\Http\Requests\Checkout;

use Illuminate\Foundation\Http\FormRequest;

class CartRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'         => ['required', 'string'],
            'address_id'      => ['required', 'string'],
            'payment_type'    => ['required', 'string'],
            'cart_item_ids'   => ['required', 'array'],
            'cart_item_ids.*' => ['required', 'string'],
            'discount_code'   => ['nullable', 'string'],
            'notes'           => ['nullable', 'string'],
            'courier'         => ['required', 'string', 'in:jne,pos,tiki,fonta'],
            'courier_service' => ['required_if:courier,jne,pos,tiki', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'cart_item_ids.required'   => 'Cart item ids is required',
            'cart_item_ids.array'      => 'Cart item ids must be an array',
            'cart_item_ids.*.required' => 'Cart item id is required',
            'cart_item_ids.*.string'   => 'Cart item id must be a string',
        ];
    }
}
