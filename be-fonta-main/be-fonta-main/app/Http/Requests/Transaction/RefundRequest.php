<?php

namespace App\Http\Requests\Transaction;

use Illuminate\Foundation\Http\FormRequest;

class RefundRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => ['required', 'string', 'max:255'],
            'notes'   => ['required', 'string', 'max:255', 'min:10'],
            'proofs'  => ['required', 'array'],

            // image and video
            'proofs.*' => ['required', 'file', 'mimes:jpg,jpeg,png,mp4,mov', 'max:3012048'],
            // 'proofs.asset' => ['nullable', 'file', 'mimes:jpg,jpeg,png,mp4', 'max:12048'],
        ];
    }
}
