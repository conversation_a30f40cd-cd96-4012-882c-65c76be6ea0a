<?php

namespace App\Http\Requests\Payout;

use Illuminate\Foundation\Http\FormRequest;

class CreatePayoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'         => ['required', 'string'],
            'amount'          => ['required', 'numeric', 'min:10000'],
            'from'            => ['required', 'in:coin,balance'],
            'otp'             => ['required', 'string', 'min:4', 'max:10'],
            'identity'        => ['required', 'string', 'min:3', 'max:36'],
            'user_account_id' => ['required', 'string', 'min:36', 'max:36'],
        ];
    }
}
