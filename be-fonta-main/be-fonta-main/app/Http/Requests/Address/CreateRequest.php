<?php

namespace App\Http\Requests\Address;

use Illuminate\Foundation\Http\FormRequest;

class CreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'     => ['required', 'string'],
            'name'        => ['required', 'string', 'max:255', 'min:3'],
            'address'     => ['required', 'string', 'max:255'],
            'postal_code' => ['required', 'string', 'min:5', 'max:5'],
            'name_receiver' => ['required', 'string', 'max:255'],
            'phone_receiver' => ['required', 'string', 'max:255'],
            'lat_lng'     => ['nullable', 'string', 'max:255'],
            'note'        => ['nullable', 'string', 'max:255'],
            'is_default'  => ['nullable', 'boolean'],
        ];
    }
}
