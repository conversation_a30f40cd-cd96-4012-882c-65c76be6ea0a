<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class GetBalanceHistoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'      => ['required', 'string'],
            'balance_type' => ['required', 'in:coin,balance'],
            'page'         => ['nullable', 'numeric'],
            'limit'        => ['nullable', 'numeric', 'max:100', 'min:1'],
            'per_page'     => ['nullable', 'numeric', 'max:100', 'min:1'],
            'start_date'   => ['nullable', 'date', 'date_format:Y-m-d'],
            'end_date'     => ['nullable', 'date', 'date_format:Y-m-d', 'after_or_equal:start_date'],
        ];
    }
}
