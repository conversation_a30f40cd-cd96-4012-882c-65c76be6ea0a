<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'  => ['required', 'string', 'max:255', 'min:3'],
            // deprecated
            'email' => [
                'nullable',
                'string',
                'email:rfc,dns',
                'max:255',
                'min:3',
            ],
            // deprecated
            'mobile_phone' => [
                'nullable',
                'string',
                'max:15',
                'min:9',
                'regex:/[0-9]{8,16}$/',
            ],
            'identity'              => ['nullable', 'string', 'max:255', 'min:3'], // replace email and mobile_phone
            'is_whatsapp'           => ['nullable', 'boolean'],
            'password'              => ['required', 'string', 'min:6', 'max:255'],
            'password_confirmation' => ['required', 'string', 'min:6', 'max:255', 'same:password'],
            'referral_code'         => ['nullable', 'string', 'max:255'],
        ];
    }
}
