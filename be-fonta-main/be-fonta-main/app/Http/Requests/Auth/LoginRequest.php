<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['nullable', 'string', 'email:strict', 'max:255', 'min:3'],
            'mobile_phone' => [
                'nullable',
                'string',
                // 'regex:/^8[0-9]{8,14}$/',
                'min:9',
                'max:15',
            ],
            'identity'              => ['nullable', 'string', 'max:255', 'min:3'], // replace email and mobile_phone
            'password' => ['required', 'string', 'min:6', 'max:255'],
        ];
    }
}
