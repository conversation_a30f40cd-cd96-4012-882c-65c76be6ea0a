<?php

namespace App\Http\Requests\Notification;

use Illuminate\Foundation\Http\FormRequest;

class StoreTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'           => ['required', 'string'],
            'firebase_token'    => ['required', 'string', 'min:10'],
            'device_id'         => ['required', 'string'],
            'device_name'       => ['required', 'string'],
            'device_type'       => ['required', 'string'],
            'device_brand'      => ['required', 'string'],
            'device_os'         => ['required', 'string'],
            'device_os_version' => ['required', 'string'],
            'device_model'      => ['required', 'string'],
        ];
    }
}
