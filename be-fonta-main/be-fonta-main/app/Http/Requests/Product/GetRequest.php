<?php

namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;

class GetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'        => ['nullable', 'string', 'max:255'],
            'sku'         => ['nullable', 'string', 'max:255'],
            'category_id' => ['nullable', 'string'],
            'category'    => ['nullable', 'string'],
            'per_page'    => ['nullable', 'integer', 'min:1', 'max:100'],
            'order_by'    => ['nullable', 'string', 'in:price'],
            'order'       => ['nullable', 'string', 'in:asc,desc'],
        ];
    }
}
