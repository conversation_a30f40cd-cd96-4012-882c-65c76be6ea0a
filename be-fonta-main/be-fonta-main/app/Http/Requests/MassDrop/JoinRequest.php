<?php

namespace App\Http\Requests\MassDrop;

use Illuminate\Foundation\Http\FormRequest;

class JoinRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'         => ['required', 'string'],
            'address_id'      => ['required', 'string'],
            'quantity'        => ['required', 'integer'],
            'payment_type'    => ['required', 'string'],
            'courier'         => ['required', 'string', 'in:jne,pos,tiki,fonta'],
            'courier_service' => ['nullable', 'string'],
            'notes'           => ['nullable', 'string'],
            // 'discount_code'   => ['nullable', 'string', 'max:255', 'min:3'],
        ];
    }
}
