<?php

namespace App\Http\Requests\MassDrop;

use Illuminate\Foundation\Http\FormRequest;

class GetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id'  => ['nullable', 'string'],
            'per_page' => ['nullable', 'integer'],
            'page'     => ['nullable', 'integer'],
            'status'   => ['nullable', 'string', 'in:coming soon'],
        ];
    }
}
