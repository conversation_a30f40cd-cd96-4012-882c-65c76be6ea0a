<?php

namespace App\Http\Resources\MassDrop;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GetMassDropResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'uuid' => $this->uuid,
        ];
    }
}
