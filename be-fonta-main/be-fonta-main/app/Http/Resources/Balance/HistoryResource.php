<?php

namespace App\Http\Resources\Balance;

use App\Models\Order;
use App\Models\Payout;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HistoryResource extends JsonResource
{
    private const TYPE_IN = 'in';

    private const SOURCE_TYPES = [
        'massdrop_cashback' => 'Massdrop Cashback',
        'transaction' => 'Transaction',
        'cancel_transaction' => 'Cancel Transaction',
        'payout' => 'Payout',
        'unknown' => 'Unknown'
    ];

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'         => $this->id,
            'amount'     => $this->amount,
            'type'       => $this->type,
            'from'       => $this->determineSource(),
            'from_id'    => $this->loggable_id,
            'from_type'  => $this->loggable_type,
            'created_at' => $this->created_at,
        ];
    }

    /**
     * Determine the source of the transaction.
     *
     * @return string
     */
    private function determineSource(): string
    {
        if ($this->isIncomingOrder()) {
            return $this->determineOrderSource();
        }

        if ($this->loggable_type === Order::class) {
            return self::SOURCE_TYPES['transaction'];
        }

        if ($this->loggable_type === Payout::class) {
            return self::SOURCE_TYPES['payout'];
        }

        return self::SOURCE_TYPES['unknown'];
    }

    /**
     * Check if the transaction is an incoming order.
     *
     * @return bool
     */
    private function isIncomingOrder(): bool
    {
        return $this->type === self::TYPE_IN && $this->loggable_type === Order::class;
    }

    /**
     * Determine the source type for an order.
     *
     * @return string
     */
    private function determineOrderSource(): string
    {
        if ($this->loggable->mess_drop_id) {
            return self::SOURCE_TYPES['massdrop_cashback'];
        }

        return self::SOURCE_TYPES['cancel_transaction'];
    }
}
