<?php

namespace App\Http\Resources\Product;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductVariantResource extends JsonResource
{
    // protected Product $product;

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'          => $this->id,
            'product_id'  => $this->product_id,
            'name'        => json_decode($this->name),
            'sku'         => $this->sku,
            'price'       => $this->price,
            'price_member' => $this->price_member,
            'stock'       => $this->stock,
            'options'     => json_decode($this->options),
            'attributes'  => $this->attributes?->map(function ($attribute) {
                return [
                    'id'          => $attribute->id,
                    'name'        => $attribute->name,
                    'option'      => [
                        'id'   => $attribute->option->id,
                        'name' => $attribute->option->name,
                    ],
                ];
            }),
            // 'product'     => [
            //     'id'          => $this->product->id,
            //     'sku'         => $this->product->sku,
            //     'name'        => $this->product->name,
            //     'description' => $this->product->description,
            //     'shipping'    => $this->product->shipping,
            //     'options'     => $this->product->options?->map(function ($option) {
            //         return [
            //             'id'     => $option->id,
            //             'name'   => $option->name,
            //             'values' => $option->values?->map(function ($value) {
            //                 return [
            //                     'id'   => $value->id,
            //                     'name' => $value->name,
            //                 ];
            //             }),
            //         ];
            //     }),
            // ],
        ];
    }

    // public function setProduct($product)
    // {
    //     $this->product = $product;
    // }
}
