<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'          => $this->id,
            'category'    => $this->category,
            'sku'         => $this->sku,
            'name'        => $this->name,
            'description' => $this->description,
            'price'       => $this->price,
            'price_member' => $this->price_member,
            'stock'       => $this->stock,
            'variants'    => $this->variants->map(function ($variant) {
                return [
                    'id'          => $variant->id,
                    'product_id'  => $variant->product_id,
                    'sku'         => $variant->sku,
                    'name'        => json_decode($variant->name),
                    'price'       => $variant->price,
                    'price_member' => $variant->price_member,
                    'stock'       => $variant->stock,
                    'options'     => json_decode($variant->options),
                    'attributes'  => $variant->attributes,
                ];
            }),
            'images'      => $this->images,
            'shipping'    => $this->shipping,
            'shippers'    => $this->shippers,
            'options'     => $this->options->map(function ($option) {
                return [
                    'id'          => $option->id,
                    'name'        => $option->name,
                    'values'      => $option->values->map(function ($variant) {
                        return [
                            'id'   => $variant->id,
                            'value' => $variant->name,
                        ];
                    }),
                ];
            }),
        ];
    }
}
