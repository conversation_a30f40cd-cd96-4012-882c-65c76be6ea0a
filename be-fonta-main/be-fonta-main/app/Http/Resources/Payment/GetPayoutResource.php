<?php

namespace App\Http\Resources\Payment;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GetPayoutResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'        => $this->id,
            'bank_name' => $this->bank_name,
            // mask account number
            'account_number' => function () {
                $len = strlen($this->account_number);

                return $len <= 4 ? $this->account_number : str_repeat('*', $len - 4) . substr($this->account_number, -4);
            },
            // mask beneficiary name
            'beneficiary_name' => function () {
                $len = strlen($this->beneficiary_name);

                return $len <= 4 ? $this->beneficiary_name : str_repeat('*', $len - 4) . substr($this->beneficiary_name, -4);
            },
            'description'     => $this->description,
            'amount'          => $this->amount,
            'status'          => $this->status,
            'type'            => $this->type,
            'from'            => $this->from,
            'reference'       => $this->reference,
            'user_id'         => $this->user_id,
            'user_account_id' => $this->user_account_id,
            'created_at'      => $this->created_at,
            'updated_at'      => $this->updated_at,
        ];
    }
}
