<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Cart\CreateRequest;
use App\Http\Requests\Cart\DecreaseQuantityRequest;
use App\Http\Requests\Cart\GetRequest;
use App\Http\Requests\Cart\IncreaseQuantityRequest;
use App\Services\CartService;
use Illuminate\Http\Request;

class CartController extends Controller
{
    private $service;

    public function __construct(
        CartService $service
    ) {
        $this->service = $service;
    }

    public function store(CreateRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->createCart($request->validated()),
            'Cart created successfully'
        );
    }

    public function index(GetRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->getCarts($request->validated()),
            'Cart retrieved successfully'
        );
    }

    public function increaseQuantity(IncreaseQuantityRequest $request, $id)
    {
        return ResponseFormatter::success(
            $this->service->increaseQuantity($request->validated(), $id),
            'Quantity increased successfully'
        );
    }

    public function decreaseQuantity(DecreaseQuantityRequest $request, $id)
    {
        return ResponseFormatter::success(
            $this->service->decreaseQuantity($request->validated(), $id),
            'Quantity decreased successfully'
        );
    }

    public function destroy(GetRequest $request, $cartId)
    {
        return ResponseFormatter::success(
            $this->service->deleteCart(['cart_detail_id' => $cartId]),
            'Cart deleted successfully'
        );
    }
}
