<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Order\CreateRequest;
use App\Services\OrderService;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    private $service;

    public function __construct(
        OrderService $service
    ) {
        $this->service = $service;
    }

    public function store(CreateRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->create($request->validated()),
            'Order created successfully'
        );
    }
}
