<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Models\Faq;
use App\Services\AppService;
use Illuminate\Http\Request;

class AppController extends Controller
{
    private $appService;

    public function __construct(
        AppService $appService
    ) {
        $this->appService = $appService;
    }

    public function getMobileVersion(Request $request)
    {
        $request->validate([
            'platform' => ['required', 'string', 'in:android,ios'],
        ]);

        return ResponseFormatter::success(
            $this->appService->getMobileVersion($request->platform),
            'Success get mobile version.'
        );
    }

    public function updateMobileVersion(Request $request)
    {
        $request->validate([
            'platform' => ['required', 'string', 'in:android,ios'],
            'version' => ['required', 'string'],
        ]);

        return ResponseFormatter::success(
            $this->appService->updateMobileVersion($request->platform, $request->version),
            'Success update mobile version.'
        );
    }

    public function getCallCenter()
    {
        return ResponseFormatter::success(
            [
                'call_center' => config('option.call_center'),
            ],
            'Success get call center.'
        );
    }

    public function getPpn()
    {
        return ResponseFormatter::success(
            [
                'tax' => config('option.tax'),
            ],
            'Success get call center.'
        );
    }


    public function getFaqs()
    {
        return ResponseFormatter::success(
            Faq::orderBy('order')->get(['question', 'answer', 'order']),
            'Success get faqs.'
        );
    }
}
