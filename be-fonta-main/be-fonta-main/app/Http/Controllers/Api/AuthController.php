<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ChangePasswordRequest;
use App\Http\Requests\Auth\CreateOtpRequest;
use App\Http\Requests\Auth\DeleteRequest;
use App\Http\Requests\Auth\GetBalanceHistoryRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\UpdateProfileRequest;
use App\Http\Requests\Auth\VerifyOtpRequest;
use App\Http\Requests\UserRequest;
use App\Services\AuthService;
use Illuminate\Http\Request;

class AuthController extends Controller
{
    private $service;

    public function __construct(
        AuthService $service
    ) {
        $this->service = $service;
    }

    public function register(RegisterRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->register($request->validated()),
            'User registered successfully',
        );
    }

    public function login(LoginRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->login($request->validated()),
            'User logged in successfully',
        );
    }

    public function currentUser(UserRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->currentUser($request->validated()),
            'User data retrieved successfully',
        );
    }

    public function changePassword(ChangePasswordRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->changePassword($request->validated()),
            'Password changed successfully',
        );
    }

    public function requestOtp(CreateOtpRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->requestOtp($request->validated()),
            'OTP sent successfully',
        );
    }

    public function verifyOtp(VerifyOtpRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->verifyOtp($request->validated()),
            'OTP verified successfully',
        );
    }

    public function balance(Request $request)
    {
        return ResponseFormatter::success(
            $this->service->balance($request->user_id),
            'Balance data retrieved successfully',
        );
    }

    public function sendResetPassword(Request $request)
    {
        // Validate the request
        $request->validate([
            'identity' => ['required', 'string', 'min:3', 'max:60'],
        ]);

        return ResponseFormatter::success(
            $this->service->forgotPassword($request->only('identity')),
            'Forgot password request sent successfully',
        );
    }

    public function resetPassword(Request $request)
    {
        // Validate the request
        $request->validate([
            'identity' => ['required', 'string', 'min:3', 'max:60'],
            'password' => ['required', 'string', 'min:6', 'max:60'],
            'otp' => ['required', 'string', 'min:4', 'max:6'],
        ]);

        return ResponseFormatter::success(
            $this->service->resetPassword($request->only('identity', 'password', 'otp')),
            'Password reset successfully',
        );
    }

    public function deleteAccount(DeleteRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->deleteAccount($request->validated()),
            'Account deleted successfully',
        );
    }

    public function updateProfile(UpdateProfileRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->updateProfile($request->validated()),
            'Profile updated successfully',
        );
    }

    public function refreshToken(Request $request)
    {
        return ResponseFormatter::success(
            $this->service->refreshToken($request->user_id),
            'Token refreshed successfully',
        );
    }

    public function balanceTransactions(GetBalanceHistoryRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->balanceTransactions($request->validated()),
            'Balance transactions retrieved successfully.',
        );
    }

    public function myReferral(UserRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->myReferral($request->validated()),
            'My referral retrieved successfully.',
        );
    }
}
