<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\MassDrop\GetRequest;
use App\Http\Requests\MassDrop\JoinRequest;
use App\Http\Requests\MassDrop\RemainRequest;
use App\Services\MassDropService;
use Illuminate\Http\Request;

class MassDropController extends Controller
{
    private $service;

    public function __construct(
        MassDropService $service
    ) {
        $this->service = $service;
    }

    public function index(GetRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->getMassDrops($request->validated()),
            'Mass Drops retrieved successfully'
        );
    }

    public function show(Request $request, $massDropId)
    {
        return ResponseFormatter::success(
            $this->service->getMassDropDetail($massDropId),
            'Mass Drop retrieved successfully'
        );
    }

    public function join(JoinRequest $request, $massDropId)
    {
        return ResponseFormatter::success(
            $this->service->joinMassDrop($request->validated(), $massDropId),
            'Joined Mass Drop successfully'
        );
    }

    public function remind(RemainRequest $request, $massDropId)
    {
        return ResponseFormatter::success(
            $this->service->remainMassDrop($request->validated(), $massDropId),
            'Remain Mass Drop successfully'
        );
    }

    public function unremind(RemainRequest $request, $massDropId)
    {
        return ResponseFormatter::success(
            $this->service->unremainMassDrop($request->validated(), $massDropId),
            'Unremain Mass Drop successfully'
        );
    }

    public function cancel(RemainRequest $request, $massDropId)
    {
        return ResponseFormatter::success(
            $this->service->cancelMassDrop($request->validated(), $massDropId),
            'Cancelled Mass Drop successfully'
        );
    }
}
