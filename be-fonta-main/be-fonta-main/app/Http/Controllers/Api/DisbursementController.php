<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Payout\CreatePayoutRequest;
use App\Http\Requests\Payout\GetPayoutRequest;
use App\Services\PaymentService;
use Illuminate\Http\Request;

class DisbursementController extends Controller
{
    private $paymentService;

    public function __construct(
        PaymentService $paymentService
    ) {
        $this->paymentService = $paymentService;
    }

    public function getBeneficiaries(Request $request)
    {
        return ResponseFormatter::success(
            $this->paymentService->getBeneficiaries($request->all()),
            'Success get beneficiaries.'
        );
    }

    public function createBeneficiary(Request $request)
    {
        return ResponseFormatter::success(
            $this->paymentService->createBeneficiary($request->all()),
            'Success create beneficiary.'
        );
    }

    public function requestPayout(CreatePayoutRequest $request)
    {
        return ResponseFormatter::success(
            $this->paymentService->requestPayout($request->all()),
            'Request payout success.'
        );
    }

    public function getPayouts(GetPayoutRequest $request)
    {
        return ResponseFormatter::success(
            $this->paymentService->getPayouts($request->validated()),
            'Success get payouts.'
        );
    }

    public function approvePayout(Request $request, $payoutId)
    {
        return ResponseFormatter::success(
            $this->paymentService->approvePayout($payoutId),
            'Success approve payout.'
        );
    }
}
