<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Services\DiscountService;
use Illuminate\Http\Request;

class DiscountController extends Controller
{
    private $service;

    public function __construct(
        DiscountService $service
    ) {
        $this->service = $service;
    }

    public function checkDiscount(Request $request)
    {
        $request->validate([
            'user_id' => ['required', 'string'],
            'discount_code' => ['required', 'string', 'max:255', 'min:3']
        ]);

        return ResponseFormatter::success(
            $this->service->checkDiscount($request->discount_code),
            'Discount code checked successfully'
        );
    }
}
