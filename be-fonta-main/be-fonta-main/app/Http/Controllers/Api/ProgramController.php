<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Program\GetRequest;
use App\Models\Program;
use App\Models\ProgramUser;
use Illuminate\Http\Request;

class ProgramController extends Controller
{
    public function getMyPrograms(GetRequest $request)
    {
        $myPrograms = Program::whereHas('users', function ($query) use ($request) {
            $query->where('user_id', $request->user_id);
        })->get();

        return ResponseFormatter::success(
            $myPrograms,
            'Data program berhasil diambil'
        );
    }
}
