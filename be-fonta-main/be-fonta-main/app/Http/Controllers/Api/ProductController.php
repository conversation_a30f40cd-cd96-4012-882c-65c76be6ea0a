<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Product\GetRequest;
use App\Services\ProductService;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    private $service;

    public function __construct(
        ProductService $service
    ) {
        $this->service = $service;
    }

    public function index(GetRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->getProducts($request->validated()),
            'Data retrieved successfully'
        );
    }

    public function show($productId)
    {
        return ResponseFormatter::success(
            $this->service->getProductById($productId),
            'Data retrieved successfully'
        );
    }

    public function getVariant(Request $request, $productId)
    {
        return ResponseFormatter::success(
            $this->service->getVariantByOptions($request->all(), $productId),
            'Data retrieved successfully'
        );
    }

    public function categories()
    {
        return ResponseFormatter::success(
            $this->service->getCategories(),
            'Data retrieved successfully'
        );
    }
}
