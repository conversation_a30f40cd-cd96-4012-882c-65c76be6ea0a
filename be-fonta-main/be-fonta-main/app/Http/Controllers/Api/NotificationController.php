<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Notification\StoreTokenRequest;
use App\Http\Requests\UserRequest;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    private $service;

    public function __construct(
        NotificationService $service
    ) {
        $this->service = $service;
    }

    public function storeToken(StoreTokenRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->storeToken($request->validated()),
            'Token stored successfully'
        );
    }

    public function getNotifications(UserRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->getNotifications($request->validated()),
            'Notifications retrieved successfully'
        );
    }

    public function markAsRead(UserRequest $request, $notificationId)
    {
        return ResponseFormatter::success(
            $this->service->markAsRead($request->validated(), $notificationId),
            'Notification marked as read successfully'
        );
    }

    public function markAllAsRead(UserRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->markAllAsRead($request->validated()),
            'All notifications marked as read successfully'
        );
    }

    public function deleteNotification(UserRequest $request, $notificationId)
    {
        return ResponseFormatter::success(
            $this->service->delete($request->validated(), $notificationId),
            'Notification deleted successfully'
        );
    }
}
