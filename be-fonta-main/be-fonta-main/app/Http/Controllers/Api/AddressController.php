<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Address\CreateRequest;
use App\Http\Requests\Address\DeleteRequest;
use App\Http\Requests\Address\GetRequest;
use App\Services\AddressService;
use Illuminate\Http\Request;

class AddressController extends Controller
{
    private $service;

    public function __construct(
        AddressService $service
    ) {
        $this->service = $service;
    }

    public function index(GetRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->getAddress($request->validated()),
            'Address retrieved successfully'
        );
    }

    public function store(CreateRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->createAddress($request->validated()),
            'Address created successfully'
        );
    }

    public function update(CreateRequest $request, $addressId)
    {
        return ResponseFormatter::success(
            $this->service->updateAddress($request->validated(), $addressId),
            'Address updated successfully'
        );
    }

    public function destroy(DeleteRequest $request, $addressId)
    {
        return ResponseFormatter::success(
            $this->service->deleteAddress($request->validated(), $addressId),
            'Address deleted successfully'
        );
    }

    public function setDefault(GetRequest $request, $addressId)
    {
        return ResponseFormatter::success(
            $this->service->setDefaultAddress($request->validated(), $addressId),
            'Default address set successfully'
        );
    }
}
