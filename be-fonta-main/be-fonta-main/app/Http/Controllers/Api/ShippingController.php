<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Shipping\CheckCostRequest;
use App\Services\ShippingService;
use Illuminate\Http\Request;

class ShippingController extends Controller
{
    private $shippingService;

    public function __construct(
        ShippingService $shippingService
    ) {
        $this->shippingService = $shippingService;
    }

    public function getProvinces(Request $request, $provinceId = null)
    {
        return ResponseFormatter::success(
            $this->shippingService->getProvinces($provinceId),
            'Provinces data retrieved successfully'
        );
    }

    public function getCities(Request $request, $cityId = null)
    {
        $request->validate([
            'province_id' => ['nullable', 'integer'],
            'name' => ['nullable', 'string', 'min:3']
        ]);

        return ResponseFormatter::success(
            $this->shippingService->getCities($cityId, $request->province_id, $request->name),
            'Cities data retrieved successfully'
        );
    }

    public function checkShippingCost(CheckCostRequest $request)
    {
        // set default courier to jne
        $request->mergeIfMissing([
            'courier' => 'jne'
        ]);

        return ResponseFormatter::success(
            $this->shippingService->checkCostByAddress($request->validated()),
            'Shipping cost retrieved successfully'
        );
    }
}
