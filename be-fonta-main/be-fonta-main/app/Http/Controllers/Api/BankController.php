<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Bank\CreateRequest;
use App\Http\Requests\Bank\GetRequest;
use App\Http\Requests\Bank\VerifyRequest;
use App\Services\BankService;
use Illuminate\Http\Request;

class BankController extends Controller
{
    private $service;

    public function __construct(
        BankService $service
    ) {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        return ResponseFormatter::success(
            $this->service->getBanks($request->all()),
            'Success get banks',
        );
    }

    public function validateAccountNumber(VerifyRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->validateAccountNumber($request->validated()),
            'Success validate account number',
        );
    }

    public function addAccount(CreateRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->addAccount($request->validated()),
            'Success add account',
        );
    }

    public function getAccounts(GetRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->getAccounts($request->validated()),
            'Success get accounts',
        );
    }

    public function deleteAccount(GetRequest $request, $accountId)
    {
        return ResponseFormatter::success(
            $this->service->deleteAccount($request->validated(), $accountId),
            'Success delete account',
        );
    }
}
