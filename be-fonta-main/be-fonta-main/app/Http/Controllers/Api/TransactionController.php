<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Transaction\GetRequest;
use App\Http\Requests\Transaction\RefundRequest;
use App\Http\Requests\UserRequest;
use App\Services\TransactionService;
use Illuminate\Http\Request;

class TransactionController extends Controller
{
    private $service;

    public function __construct(
        TransactionService $service
    ) {
        $this->service = $service;
    }

    public function index(GetRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->getTransactions($request->validated()),
            'Transactions retrieved successfully'
        );
    }

    public function checkStatus($transactionId)
    {
        return ResponseFormatter::success(
            $this->service->checkStatusPayment($transactionId),
            'Transaction status retrieved successfully'
        );
    }

    public function downloadInvoice($invoiceNumber)
    {
        return $this->service->generateInvoice($invoiceNumber);
    }

    public function cancel(GetRequest $request, $orderId)
    {
        return ResponseFormatter::success(
            $this->service->cancelTransaction($request->validated(), $orderId),
            'Transaction cancelled successfully'
        );
    }

    public function refund(RefundRequest $request, $orderId)
    {
        return ResponseFormatter::success(
            $this->service->refund($request->validated(), $orderId),
            'Transaction refunded successfully'
        );
    }

    public function detail(UserRequest $request, $transactionId)
    {
        return ResponseFormatter::success(
            $this->service->getTransactionDetail($request->validated(), $transactionId),
            'Transaction detail retrieved successfully'
        );
    }

    public function complete(UserRequest $request, $transactionId)
    {
        return ResponseFormatter::success(
            $this->service->done($request->validated(), $transactionId),
            'Transaction detail retrieved successfully'
        );
    }
}
