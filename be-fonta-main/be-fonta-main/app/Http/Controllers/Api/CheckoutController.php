<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ResponseFormatter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Checkout\CartRequest;
use App\Http\Requests\Checkout\DirectBuyRequest;
use App\Services\CheckoutService;
use App\Services\PaymentService;
use Illuminate\Http\Request;

class CheckoutController extends Controller
{
    private $service;
    private $paymentService;

    public function __construct(
        CheckoutService $service,
        PaymentService $paymentService
    ) {
        $this->service = $service;
        $this->paymentService = $paymentService;
    }

    public function index(DirectBuyRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->checkoutByProduct($request->validated()),
            'Payment created successfully',
        );
    }

    public function paymentMethods()
    {
        return ResponseFormatter::success(
            $this->paymentService->getPaymentMethods(),
            'Payment methods retrieved successfully',
        );
    }

    public function checkoutCart(CartRequest $request)
    {
        return ResponseFormatter::success(
            $this->service->checkoutCart($request->validated()),
            'Payment created successfully',
        );
    }
}
