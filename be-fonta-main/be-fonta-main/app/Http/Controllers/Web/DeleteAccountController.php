<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\RequestDeleteAccount;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class DeleteAccountController extends Controller
{
    public function index()
    {
        return inertia('DeleteAccount');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name'           => ['required', 'string', 'max:255', 'min:3'],
            'email_or_phone' => ['required', 'string', 'max:255', 'min:3'],
            'reason'         => ['required', 'string', 'max:255'],
            'password'       => ['required', 'string', 'min:6', 'max:255'],
        ]);

        $isNumber = is_numeric($request->email_or_phone);

        if ($isNumber) {
            // convert to phone number
            $phone = toMobilePhone($request->email_or_phone);

            // find user by phone number
            $user = User::where('mobile_phone', $phone)->first();

            // validate password
            if ($user && Hash::check($request->password, $user->password)) {
                // delete user
                RequestDeleteAccount::create([
                    'name'         => $request->name,
                    'mobile_phone' => $user->mobile_phone,
                    'reason'       => $request->reason,
                ]);

                // return back with success message
                return redirect()
                    ->route('delete-account.index')
                    ->with('success', 'Your account has been deleted successfully.');
            }

            throw ValidationException::withMessages(['error' => 'Invalid email/phone or password.']);
        }

        // find user by email
        $user = User::where('email', $request->email_or_phone)->first();

        // validate password
        if ($user && Hash::check($request->password, $user->password)) {
            // delete user
            RequestDeleteAccount::create([
                'name'   => $request->name,
                'email'  => $user->email,
                'reason' => $request->reason,
            ]);

            // return back with success message
            return redirect()
                ->route('delete-account.index')
                ->with('success', 'Your account has been deleted successfully.');
        }

        // validate error with message
        throw ValidationException::withMessages(['error' => 'Invalid email/phone or password.']);
    }
}
