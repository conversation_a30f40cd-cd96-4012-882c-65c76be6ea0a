<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    public function index()
    {
        return inertia('Admin/Setting/Index');
    }

    public function paymentMethods()
    {
        return inertia('Admin/Setting/PaymentMethods', [
            'paymentMethods' => PaymentMethod::all(),
        ]);
    }

    public function paymentMethodStatus(Request $request, $paymentMethodId)
    {
        $request->validate([
            'is_active' => ['required', 'boolean'],
        ]);

        $paymentMethod = PaymentMethod::findOrFail($paymentMethodId);

        $paymentMethod->update([
            'is_active' => $request->is_active,
        ]);

        return response()->json([
            'message' => 'Payment method status updated successfully',
        ]);
    }

    public function paymentMethodStore(Request $request)
    {
        $request->validate([
            'name'       => ['required', 'string', 'max:255'],
            'code'       => ['required', 'string', 'max:255'],
            'channel'    => ['required', 'string', 'max:255', 'in:E-Wallet,Bank Transfer,QRIS'],
            'provider'   => ['required', 'string', 'max:255'],
            'sort_order' => ['required', 'integer', 'min:1'],
            'fee'        => ['required', 'numeric', 'min:0'],
            'fee_type'   => ['required', 'string', 'max:255', 'in:percentage,fixed'],
            'is_active'  => ['required', 'boolean'],
            'icon_file'  => ['nullable', 'file', 'mimes:jpeg,jpg,png', 'max:2048'],
        ]);

        $iconPath = null;
        if ($request->hasFile('icon_file')) {
            $iconPath = uploadFile($request->file('icon_file'), 'payment-method');
        }

        PaymentMethod::create([
            'name'       => $request->name,
            'code'       => $request->code,
            'channel'    => $request->channel,
            'provider'   => $request->provider,
            'sort_order' => $request->sort_order,
            'fee'        => $request->fee,
            'fee_type'   => $request->fee_type,
            'is_active'  => $request->is_active,
            'icon_path'  => $iconPath,
        ]);

        return redirect()
            ->route('v1.admin.settings.paymentMethods')
            ->with('success', 'Payment method created successfully');
    }

    public function updatePaymentMethod(Request $request, $paymentMethodId)
    {
        $request->validate([
            'name'       => ['required', 'string', 'max:255'],
            'code'       => ['required', 'string', 'max:255'],
            'channel'    => ['required', 'string', 'max:255', 'in:E-Wallet,Bank Transfer,QRIS'],
            'provider'   => ['required', 'string', 'max:255'],
            'sort_order' => ['required', 'integer', 'min:1'],
            'fee'        => ['required', 'numeric', 'min:0'],
            'fee_type'   => ['required', 'string', 'max:255', 'in:percentage,fixed'],
            'is_active'  => ['required', 'boolean'],
            'icon_file'  => ['nullable', 'file', 'mimes:jpeg,jpg,png', 'max:2048'],
        ]);

        $paymentMethod = PaymentMethod::findOrFail($paymentMethodId);

        if ($request->hasFile('icon_file')) {
            $path = uploadFile($request->file('icon_file'), 'payment-method');

            $paymentMethod->update([
                'icon_path' => $path,
            ]);
        }

        $paymentMethod->update([
            'name'       => $request->name,
            'code'       => $request->code,
            'channel'    => $request->channel,
            'provider'   => $request->provider,
            'sort_order' => $request->sort_order,
            'fee'        => $request->fee,
            'fee_type'   => $request->fee_type,
            'is_active'  => $request->is_active,
        ]);

        return redirect()
            ->route('v1.admin.settings.paymentMethods')
            ->with('success', 'Payment method updated successfully');
    }

    public function deletePaymentMethod($paymentMethodId)
    {
        $paymentMethod = PaymentMethod::findOrFail($paymentMethodId);

        $paymentMethod->delete();

        return redirect()
            ->route('v1.admin.settings.paymentMethods')
            ->with('success', 'Payment method deleted successfully');
    }
}
