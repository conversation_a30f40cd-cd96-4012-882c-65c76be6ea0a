<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductOption;
use App\Models\ProductOptionVariant;
use App\Models\ProductProductOption;
use App\Models\ProductVariant;
use App\Models\ProductVariantOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Facades\LogBatch;

class VariantController extends Controller
{
    public function getVariant($productId)
    {
        $product = Product::with([
            'variants',
            'options.values' => function ($query) {
                $query->select('id', 'product_option_id', 'name');
            },
        ])->findOrFail($productId);

        // Manipulate the values to only include the 'name'
        $product->options->each(function ($option) {
            $option->values = $option->values->map(function ($value) {
                return [
                    'id'   => $value->id,
                    'name' => $value['name'],
                ];
            });
        });

        return inertia('Admin/Product/Variant', [
            'product' => $product,
            'options' => $product->options->map(function ($option) {
                return [
                    'id'     => $option->id,
                    'name'   => $option->name,
                    'values' => $option->values,
                ];
            }),
        ]);
    }

    public function storeVariantOption(Request $request, $productId)
    {
        $request->validate([
            'name'          => ['required', 'string', 'max:60', 'min:3'],
            'values'        => ['required', 'array'],
            'values.*.name' => ['required', 'string', 'max:60', 'min:1'],
        ]);

        $product = Product::findOrFail($productId);

        $newVariantOption = null;

        DB::transaction(function () use ($request, $product) {
            $newVariantOption = ProductOption::create([
                'name' => $request->name,
            ]);

            foreach ($request->values as $value) {
                ProductOptionVariant::create([
                    'product_option_id' => $newVariantOption->id,
                    'name'              => $value['name'],
                ]);
            }

            ProductProductOption::create([
                'product_id'        => $product->id,
                'product_option_id' => $newVariantOption->id,
            ]);
        });

        return redirect()
            ->back()
            ->with('Option created successfully.');
    }

    public function updateVariantOption(Request $request, string $optionId)
    {
        $request->validate([
            'name'          => ['required', 'string', 'max:60', 'min:3'],
            'values'        => ['required', 'array'],
            'values.*.name' => ['required', 'string', 'max:60', 'min:1'],
        ]);

        DB::transaction(function () use ($request, $optionId) {
            $variantOption = ProductOption::findOrFail($optionId);

            $variantOption->update([
                'name' => $request->name,
            ]);

            $variantOption->values()->delete();

            foreach ($request->values as $value) {
                ProductOptionVariant::create([
                    'product_option_id' => $variantOption->id,
                    'name'              => $value['name'],
                ]);
            }
        });

        return redirect()->back()->with('success', 'Option updated successfully.');
    }

    public function storeVariants(Request $request, $productId)
    {
        $request->validate([
            '*.id'               => ['nullable', 'string'],
            '*.optionIds'        => ['nullable', 'array'],
            '*.optionIds.*.id'   => ['required', 'max:60', 'min:1'],
            '*.optionIds.*.name' => ['required', 'string', 'max:60', 'min:1'],
            '*.sku'              => ['required', 'string', 'max:60', 'min:3'],
            '*.price'            => ['required', 'numeric', 'min:0'],
            '*.price_member'     => ['required', 'numeric', 'min:0'],
            '*.stock'            => ['required', 'numeric', 'min:0'],
        ]);

        $product = Product::findOrFail($productId);

        $productVariantExists = ProductVariant::where('product_id', $product->id)
            ->get();

        DB::transaction(function () use ($request, $product, $productVariantExists) {
            LogBatch::startBatch();
            foreach ($request->all() as $variant) {
                // check if the variant already exists
                $variantId = $variant['id'] ?? null;

                $productVariant = $productVariantExists->where('id', $variantId)->first();

                if ($productVariant || $productVariant?->sku ===  $variant['sku']) {
                    $productVariant->update([
                        'name'         => json_encode($variant['options']),
                        'options'      => json_encode($variant['optionIds']),
                        'sku'          => $variant['sku'],
                        'price'        => $variant['price'],
                        'price_member' => $variant['price_member'],
                        'stock'        => $variant['stock'],
                    ]);

                    ProductVariantOption::where('product_variant_id', $productVariant->id)
                        ->delete();

                    foreach ($variant['optionIds'] as $option) {
                        ProductVariantOption::create([
                            'product_variant_id'        => $productVariant->id,
                            'product_option_variant_id' => $option['id'],
                        ]);
                    }

                    continue;
                }

                $newVariant = ProductVariant::create([
                    'product_id'   => $product->id,
                    'name'         => json_encode($variant['options']),
                    'options'      => json_encode($variant['optionIds']),
                    'sku'          => $variant['sku'],
                    'price'        => $variant['price'],
                    'price_member' => $variant['price_member'],
                    'stock'        => $variant['stock'],
                ]);

                foreach ($variant['optionIds'] as $option) {
                    ProductVariantOption::create([
                        'product_variant_id'        => $newVariant->id,
                        'product_option_variant_id' => $option['id'],
                    ]);
                }
            }

            // delete where not in sku
            ProductVariant::where('product_id', $product->id)
                ->whereNotIn('sku', collect($request->all())->pluck('sku'))
                ->delete();

            LogBatch::endBatch();
        });

        return redirect()->back()->with('success', 'Variants created successfully.');
    }

    public function deleteVariantOption($optionId)
    {
        $option = ProductOption::findOrFail($optionId);

        $option->delete();

        return redirect()->back()->with('success', 'Option deleted successfully.');
    }

    public function deleteVariant($variantId)
    {
        $variant = ProductVariant::findOrFail($variantId);

        $variant->delete();

        return redirect()->back()->with('success', 'Variant deleted successfully.');
    }
}
