<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Admin/Category/Index', [
            'categories' => ProductCategory::when($request->search, function ($query, $search) {
                return $query->where('name', 'like', "%$search%");
            })
                ->orderByDesc('created_at')
                ->paginate(15),
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'min:3'],
        ]);

        // check if category already exists
        $category = ProductCategory::where('name', $request->name)->first();

        if ($category) {
            throw ValidationException::withMessages([
                'name' => 'Category already exists',
            ]);
        }

        ProductCategory::create($request->only('name'));

        return redirect()->back()->with('success', 'Category created successfully');
    }

    public function update(Request $request, $categoryId)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'min:3'],
        ]);

        $category = ProductCategory::find($categoryId);

        if (!$category) {
            throw ValidationException::withMessages([
                'name' => 'Category not found',
            ]);
        }

        // check if category already exists
        if (ProductCategory::where('name', $request->name)
            ->where('id', '!=', $categoryId)
            ->exists()
        ) {
            throw ValidationException::withMessages([
                'name' => 'Category already exists',
            ]);
        }

        $category->update($request->only('name'));

        return redirect()->back()->with('success', 'Category updated successfully');
    }

    public function destroy($categoryId)
    {
        $category = ProductCategory::find($categoryId);

        if (!$category) {
            throw ValidationException::withMessages([
                'name' => 'Category not found',
            ]);
        }

        $category->delete();

        return redirect()->back()->with('success', 'Category deleted successfully');
    }
}
