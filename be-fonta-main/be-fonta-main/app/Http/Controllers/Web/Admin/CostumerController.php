<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class CostumerController extends Controller
{
    public function index(Request $request)
    {
        /** @disregard */
        return inertia('Admin/Sales/Costumer', [
            // all users has role 'costumer'
            'costumers' => User::with(['roles', 'referrals', 'referBy.referredBy'])
                ->when(isset($request->search), function ($query) use ($request) {
                    $query->where(function ($query) use ($request) {
                        $query->where('name', 'like', '%' . $request->search . '%')
                            ->orWhere('email', 'like', '%' . $request->search . '%')
                            ->orWhere('mobile_phone', 'like', '%' . $request->search . '%');
                    });
                })
                ->when(isset($request->start_date), function ($query) use ($request) {
                    $query->whereDate('created_at', '>=', $request->start_date);
                })
                ->when(isset($request->end_date), function ($query) use ($request) {
                    $query->whereDate('created_at', '<=', $request->end_date);
                })
                ->when(isset($request->order_by), function ($query) use ($request) {
                    if ($request->order_by === 'referrals_count') {
                        $query->withCount('referrals')
                            ->orderBy('referrals_count', $request->order ?? 'desc');
                    }
                }, function ($query) {
                    $query->orderBy('created_at', 'desc');
                })
                ->paginate(10)
                ->withQueryString(),
        ]);
    }
}
