<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payout;
use App\Models\UserBalance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class PayoutController extends Controller
{
    public function index(Request $request)
    {
        $payouts = Payout::with(['user'])
            ->when(isset($request->search), function ($query) use ($request) {
                $query->whereHas('user', function ($query) use ($request) {
                    $query->where('name', 'like', '%' . $request->search . '%');
                });
            })
            ->when(isset($request->status), function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when(isset($request->start_date), function ($query) use ($request) {
                $query->whereDate('created_at', '>=', $request->start_date);
            })
            ->when(isset($request->end_date), function ($query) use ($request) {
                $query->whereDate('created_at', '<=', $request->end_date);
            })
            ->latest()
            // ->withQueryString()
            ->paginate(10);

        return inertia('Admin/Payout/Index', [
            'payouts' => $payouts,
        ]);
    }

    public function approve($payoutId)
    {
        $payout = Payout::find($payoutId);

        // only approve if status is pending
        if ($payout->status !== Payout::STATUS_PENDING) {
            throw ValidationException::withMessages([
                'message' => 'Payout cannot be approved.'
            ]);
        }

        DB::beginTransaction();

        try {
            if (! $payout) {
                throw ValidationException::withMessages([
                    'message' => 'Payout not found.'
                ]);
            }

            $payout->update([
                'status' => Payout::STATUS_SUCCESS,
            ]);

            // deduct user balance
            $userBalance = UserBalance::where('user_id', $payout->user_id)
                ->first();

            // check if user balance exists
            if (! $userBalance) {
                throw ValidationException::withMessages([
                    'message' => 'User balance not found.'
                ]);
            }

            // validate amount
            if ($userBalance->coin < $payout->amount) {
                throw ValidationException::withMessages([
                    'message' => 'Saldo tidak mencukupi.'
                ]);
            }

            if ($userBalance) {
                $userBalance->logs()->create([
                    'user_id'       => $payout->user_id,
                    'amount'        => $payout->amount,
                    'new_amount'    => $userBalance->coin - $payout->amount,
                    'old_amount'    => $userBalance->coin,
                    'type'          => 'out',
                    'balance_type'  => 'coin',
                    'loggable_id'   => $payout->id,
                    'loggable_type' => Payout::class,
                ]);

                $userBalance->update([
                    'coin' => $userBalance->coin - $payout->amount,
                ]);
            }

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }

        return redirect()->back()->with('success', 'Payout approved successfully.');
    }

    public function reject($payoutId)
    {
        $payout = Payout::findOrFail($payoutId);

        // only approve if status is pending
        if ($payout->status !== Payout::STATUS_PENDING) {
            throw ValidationException::withMessages([
                'message' => 'Payout cannot be rejected.'
            ]);
        }

        $payout->update(['status' => 'rejected']);

        return redirect()->back()->with('success', 'Payout rejected successfully.');
    }
}
