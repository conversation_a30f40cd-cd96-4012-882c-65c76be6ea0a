<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Distributor;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\OrderShipping;
use App\Services\TransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Spatie\Browsershot\Browsershot;
use Spatie\LaravelPdf\Facades\Pdf;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        // Validate request
        $request->validate([
            'search'       => ['nullable', 'string'],
            'status'       => ['nullable', 'string'],
            'start_date'   => ['nullable', 'date'],
            'end_date'     => ['nullable', 'date'],
            'payment_type' => ['nullable', 'string'],
            'per_page'     => ['nullable', 'integer'],
        ]);

        return inertia('Admin/Order/Index', [
            'orders' => Order::with([
                'orderItems.product.images' => function ($query) {
                    $query
                        ->limit(1)
                        ->orderBy('is_default', 'desc');
                },
                'distributor',
                'address',
                'user',
                'shipping',
                'transactions' => function ($query) {
                    $query->limit(1)->orderBy('created_at', 'desc');
                }
            ])
                ->when(isset($request->payment_type), function ($query) use ($request) {
                    $query->whereHas('transactions', function ($query) use ($request) {
                        $query->where('payment_type', $request->payment_type);
                    });
                })
                ->when(isset($request->search), function ($query) use ($request) {
                    $query
                        ->where('id', 'like', '%' . $request->search . '%')
                        ->orWhereHas('user', function ($query) use ($request) {
                            $query->where('name', 'like', '%' . $request->search . '%');
                        });
                })
                ->when(isset($request->status), function ($query) use ($request) {
                    $query->where('status', $request->status);
                })
                ->when(isset($request->start_date), function ($query) use ($request) {
                    $query->whereDate('created_at', '>=', $request->start_date);
                })
                ->when(isset($request->end_date), function ($query) use ($request) {
                    $query->whereDate('created_at', '<=', $request->end_date);
                })
                ->orderBy('created_at', 'desc')
                ->paginate($request->per_page ?? 15)
                ->withQueryString(),
        ]);
    }

    public function show($orderId)
    {
        return inertia('Admin/Order/Show', [
            'order' => Order::with([
                'transactions' => function ($query) {
                    $query
                        ->limit(1)
                        ->orderBy('created_at', 'desc');
                },
                'distributor',
                'address',
                'user',
                'orderItems.product.images' => function ($query) {
                    $query
                        ->limit(1)
                        ->orderBy('is_default', 'desc');
                },
                'orderItems.productVariant',
                'shipping',
                'logs',
            ])
                ->findOrFail($orderId),
            'distributors' => Distributor::all('id', 'name', 'postal_code'),
        ]);
    }

    public function updateStatus(Request $request, $orderId)
    {
        // Validate request
        $request->validate([
            'status' => ['required', 'string', 'in:pending,paid,on delivery,delivered,done,refunded'],
        ]);

        $order = Order::findOrFail($orderId);

        // if (in_array($order->status, [
        //     Order::STATUS_CANCELLED,
        // ])) {
        //     throw ValidationException::withMessages([
        //         'status' => 'Status cannot be updated',
        //     ]);
        // }

        // // check if status DONE
        // if (
        //     $order->status === Order::STATUS_DONE
        //     && $order->updated_at->diffInDays(now()) > 2
        // ) {
        //     // check if Update has 2 day ago
        //     return response()->json([
        //         'status'  => false,
        //         'message' => 'Order status cannot be updated, because it has been more than 2 days',
        //     ], 400);
        // }

        // $order->update([
        //     'status' => $request->status,
        // ]);

        $this->updateStatusOrder($order, $request->status);

        return response()->json([
            'status'  => true,
            'message' => 'Order status updated',
        ]);
    }

    private function updateStatusOrder(Order $order, string $status)
    {
        if (!in_array($order->status, [
            Order::STATUS_PENDING,
            Order::STATUS_PAID,
            Order::STATUS_ON_DELIVERY,
            Order::STATUS_DELIVERED,
        ])) {
            throw ValidationException::withMessages([
                'messages' => 'Status cannot be updated, because it is not in the allowed status',
            ]);
        }

        // status paid can only be updated to on delivery, delivered, done
        if ($order->status === Order::STATUS_PAID && (!in_array($status, [
            Order::STATUS_ON_DELIVERY,
            Order::STATUS_DELIVERED,
            Order::STATUS_DONE,
        ]))) {
            throw ValidationException::withMessages([
                'messages' => 'Status cannot be updated, because it is not in the allowed status',
            ]);
        }

        if (in_array($order->status, [
            Order::STATUS_CANCELLED,
        ])) {
            throw ValidationException::withMessages([
                'messages' => 'Status cannot be updated',
            ]);
        }

        // check if status DONE
        if (
            $order->status === Order::STATUS_DONE
            && $order->updated_at->diffInDays(now()) > 2
        ) {
            throw ValidationException::withMessages([
                'messages' => 'Order status cannot be updated, because it has been more than 2 days',
            ]);
        }



        $order->update([
            'status' => $status,
        ]);
    }

    public function updateDistributor(Request $request, $orderId)
    {
        // Validate request
        $request->validate([
            'distributor_id' => ['required', 'string', 'min:36', 'max:36'],
        ]);

        $order = Order::findOrFail($orderId);

        $order->update([
            'distributor_id' => $request->distributor_id,
        ]);

        return response()->json([
            'status'  => true,
            'message' => 'Order distributor updated',
        ]);
    }

    public function downloadInvoice($orderId)
    {
        return app(TransactionService::class)->generateInvoice($orderId);
    }

    public function downloadAddress(Request $request)
    {
        // Validate request
        $request->validate([
            'status' => ['required', 'string'],
        ]);

        // get all orders
        $orders = Order::with(['address'])
            ->select('id', 'address_id', 'order_code', 'total', 'created_at', 'invoice_number', 'notes')
            ->where('status', $request->status)
            ->get();

        $addresses = $orders->map(function ($order) {
            return [
                'code'  => $order->invoice_number,
                'date'  => $order->created_at->format('Y-m-d'),
                'total' => $order->total,
                'items' => $order->orderItems->map(function ($item) {
                    return [
                        'name'     => $item->product->name ?? null,
                        'quantity' => $item->quantity,
                        'price'    => $item->price,
                    ];
                }),
                'recipient' => [
                    'name'        => $order->address->name_receiver ?? null,
                    'phone'       => $order->address->phone_receiver ?? null,
                    'address'     => $order->address->address ?? null,
                    'city'        => $order->address->city ?? null,
                    'province'    => $order->address->province ?? null,
                    'postal_code' => $order->address->postal_code ?? null,
                    'note'        => $order->notes ?? null,
                ],

                'sender' => [
                    'name'        => 'Fonta',
                    'phone'       => '62 852-8372-8394',
                    'address'     => 'PT. Samudera Marga Wisesa Ruko Duta Gardenia Blok D/28, Jurumudi Baru, Tangerang, Banten 15124',
                    'city'        => 'Jurumudi Baru, Tangerang',
                    'province'    => 'Banten',
                    'postal_code' => '15124',
                    'note'        => '',
                ],
            ];
        });

        // dd($addresses);

        return Pdf::view('pdf.address', [
            'orders' => $addresses,
        ])
            ->name('address' . date('Y-m-d h:s') . '.pdf')
            ->withBrowsershot(function (Browsershot $browsershot) {
                $browsershot
                    ->noSandbox()
                    ->setNodeBinary(config('option.path.node_binary'))
                    ->setNpmBinary(config('option.path.npm_binary'));
            })
            // termal printer
            ->paperSize(10, 15, 'cm')
            ->download();
    }

    public function updateTrackingNumber(Request $request, $orderId)
    {
        // Validate request
        $request->validate([
            'tracking_number' => ['required', 'string', 'max:255'],
        ]);

        $orderShipping = OrderShipping::where('order_id', $orderId)->first();

        if (! $orderShipping) {
            throw ValidationException::withMessages([
                'message' => 'Order shipping not found',
            ]);
        }

        $orderShipping->update([
            'tracking_number' => $request->tracking_number,
        ]);

        return redirect()->back()->with('success', 'Tracking number updated');
    }

    public function exportOrderCsv(Request $request)
    {
        // validate request
        $request->validate([
            'search'     => ['nullable', 'string'],
            'status'     => ['nullable', 'string'],
            'start_date' => ['required', 'date', 'before_or_equal:today'],
            'end_date'   => ['required', 'date', 'after_or_equal:start_date'],
        ]);

        $orders = OrderDetail::with([
            // 'orderItems.product',
            'order.user' => function ($query) {
                $query->select('id', 'name');
            },
            'order.address' => function ($query) {
                $query->select('id', 'address', 'postal_code');
            },
            'order.shipping' => function ($query) {
                $query->select('id', 'order_id', 'carrier', 'service', 'cost');
            },
            'product' => function ($query) {
                $query->select('id', 'name');
            },
        ])
            ->whereRelation('order', 'created_at', '>=', $request->start_date)
            ->whereRelation('order', 'created_at', '<=', $request->end_date)
            ->when(isset($request->status), function ($query) use ($request) {
                $query->whereRelation('order', 'status', $request->status);
            })
            ->select([
                'id',
                'order_id',
                'product_id',
                'quantity',
                'price',
            ])
            ->get();

        $callback = function () use ($orders) {
            $file = fopen('php://output', 'w');

            fputcsv($file, [
                'No',
                'Name',
                'Invoice',
                'Product',
                'Quantity',
                'Price',
                'Total',
                'Sub Total',
                'Shipping Cost',
                'Admin Fee',
                'Discount',
                'Order Date',
                'Status',
                'Courier',
                'Address',
                'Postal Code',
            ]);

            $no = 1;
            foreach ($orders as $order) {

                fputcsv($file, [
                    $no,
                    $order->order->user->name ?? null,
                    $order->order->invoice_number ?? null,
                    $order->product->name ?? null,
                    $order->quantity,
                    $order->price,
                    $order->order->total ?? 0,
                    $order->order->sub_total ?? 0,
                    $order->order->shipping_cost ?? 0,
                    $order->order->admin_fee ?? 0,
                    $order->order->discount_total ?? 0,
                    $order->order->created_at,
                    $order->order->status,
                    $order->order->shipping->carrier ?? null,
                    $order->order->address->address ?? '-',
                    $order->order->address->postal_code ?? '-',
                ]);

                $no++;
            }

            fclose($file);
        };

        return response()->stream(
            $callback,
            200,
            [
                'Content-Type'        => 'text/csv',
                'Content-Disposition' => 'attachment; filename="orders.csv"',
                'filename'            => 'orders-' . date('Ymdhs') . '.csv',
            ]
        );
    }

    public function bulkStatus(Request $request)
    {
        // validate
        $request->validate([
            'order_ids'    => ['required', 'array'],
            'order_ids.*'  => ['required', 'string', 'min:36', 'max:36'],
            'status' => ['required', 'string', 'in:pending,paid,on delivery,delivered,done,refunded'],
        ]);

        $orders = Order::whereIn('id', $request->order_ids)->get();

        if ($orders->isEmpty()) {
            throw ValidationException::withMessages([
                'messages' => 'Order not found',
            ]);
        }

        DB::beginTransaction();

        try {
            $orders->each(function ($order) use ($request) {
                $this->updateStatusOrder($order, $request->status);
            });

            DB::commit();

            return redirect()->back()->with('success', 'Order status updated');
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }
}
