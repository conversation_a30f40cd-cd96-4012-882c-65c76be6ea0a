<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Program;
use App\Models\ProgramUser;
use Illuminate\Http\Request;

class ProgramController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Admin/Program/Index', [
            'programs' => Program::when($request->search, function ($query, $search) {
                return $query->where('name', 'like', "%$search%");
            })
                ->orderByDesc('created_at')
                ->when($request->with_trashed === "true", function ($query, $withTrashed) {
                    return $query->withTrashed();
                })
                ->paginate(15),
        ]);
    }

    public function create()
    {
        return inertia('Admin/Program/Create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'min:15'],
            'description' => ['required', 'string', 'min:3'],
        ]);

        $slug = str()->slug($request->name);

        Program::create([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
        ]);

        return redirect()
            ->route('v1.admin.programs')
            ->with('success', 'Program created successfully');
    }

    public function show(string $programId)
    {
        return inertia('Admin/Program/Show', [
            'program' => Program::with(['users'])
                ->findOrFail($programId),
        ]);
    }

    public function edit(string $programId)
    {
        return inertia('Admin/Program/Edit', [
            'program' => Program::findOrFail($programId),
        ]);
    }

    public function update(Request $request, string $programId)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'min:15'],
            'description' => ['required', 'string', 'min:3'],
        ]);


        $slug = str()->slug($request->name);

        $program = Program::findOrFail($programId);

        $program->update([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
        ]);

        return redirect()
            ->route('v1.admin.programs')
            ->with('success', 'Program updated successfully');
    }

    public function destroy(string $programId)
    {
        $program = Program::findOrFail($programId);

        $program->delete();

        return redirect()
            ->route('v1.admin.programs')
            ->with('success', 'Program deleted successfully');
    }

    public function assign(Request $request, string $programId)
    {
        $request->validate([
            'user_ids' => ['required', 'array'],
            'user_ids.*' => ['required', 'string', 'exists:users,id'],
        ]);

        $program = Program::findOrFail($programId);

        foreach ($request->user_ids as $userId) {
            ProgramUser::firstOrCreate([
                'program_id' => $program->id,
                'user_id' => $userId,
            ]);
        }

        return redirect()
            ->route('v1.admin.programs.show', $programId)
            ->with('success', 'User assigned to program successfully');
    }

    public function unassign(string $programId, string $userId)
    {
        $program = Program::findOrFail($programId);

        ProgramUser::where('program_id', $program->id)
            ->where('user_id', $userId)
            ->delete();

        return redirect()
            ->route('v1.admin.programs.show', $programId)
            ->with('success', 'User unassigned from program successfully');
    }
}
