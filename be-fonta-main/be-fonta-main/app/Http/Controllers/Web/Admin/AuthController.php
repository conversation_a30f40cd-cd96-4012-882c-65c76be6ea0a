<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\VerificationToken;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Auth/Login');
    }

    public function doLogin(Request $request)
    {
        $request->validate([
            'email'    => 'required|email',
            'password' => 'required',
            'remember' => ['nullable', 'boolean'],
        ]);

        if (auth('web')->attempt($request->only('email', 'password'), $request->remember)) {
            /** @disregard */
            if (
                auth('web')->user()->isSuperAdmin()
                || auth('web')->user()->isAdmin()
            ) {

                return redirect()->route('v1.admin.dashboard');
            }

            // logout
            auth('web')->logout();

            return redirect()->back()->with('error', 'Invalid credentials');
        }

        return redirect()->back()->with('error', 'Invalid credentials');
    }

    public function verificationToken(Request $request)
    {
        $request->validate([
            'token' => ['required', 'string'],
        ]);

        $token = VerificationToken::where('token', $request->token)->first();

        if ($token) {
            // verify user
            User::where('email', $token->identity)
                ->update([
                    'email_verified_at' => now(),
                ]);

            // delete token
            $token->delete();
        }

        return inertia('Mail/Verification', [
            'isSuccess' => $token ? true : false,
        ]);
    }

    public function doLogout(Request $request)
    {
        auth('web')->logout();

        return redirect()->route('v1.admin.auth.login');
    }

    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password'      => ['required', 'string'],
            'password'              => ['required', 'string', 'min:8'],
            'password_confirmation' => ['required', 'string', 'same:password'],
        ]);

        $emil = auth('web')->user()->email;

        $request->merge([
            'email' => $emil,
        ]);

        if (auth('web')->attempt([
            'email' => $emil,
            'password' => $request->current_password
        ])) {

            /** @disregard */
            auth('web')->user()->update([
                'password' => Hash::make($request->password),
            ]);

            return redirect()->back()->with('success', 'Password changed successfully');
        }


        throw ValidationException::withMessages([
            'messages' => "Failed to change password, please try again",
        ]);
    }
}
