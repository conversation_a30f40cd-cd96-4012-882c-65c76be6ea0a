<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Discount;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class DiscountController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Admin/Discount/Index', [
            'discounts' => Discount::orderBy('created_at', 'desc')
                ->paginate($request->per_page ?? 15),
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string'],
            'code' => ['required', 'string'],
            'type' => ['required', 'string', 'in:percentage,fixed'],
            'value' => ['required', 'numeric'],
            'usage_limit' => ['required', 'numeric'],
            'usage_limit_per_user' => ['required', 'numeric'],
            'starts_at' => ['required', 'date'],
            'ends_at' => ['required', 'date'],
        ]);

        $request->merge([
            'created_by' => $request->user()->id,
        ]);

        // check if the discount code is already exist
        $discount = Discount::where('code', $request->code)
            ->first();
        if ($discount) {
            throw ValidationException::withMessages([
                'code' => 'The code has already been taken.',
            ]);
        }

        Discount::create($request->all());

        return redirect()->route('v1.admin.discounts');
    }

    public function destroy(Request $request, $discountId)
    {
        $discount = Discount::findOrFail($discountId);

        $discount->delete();

        return redirect()->route('v1.admin.discounts');
    }
}
