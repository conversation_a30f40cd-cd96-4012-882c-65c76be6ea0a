<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductImage;
use App\Models\Shipper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Admin/Product/Index', [
            'products' => Product::with([
                'images' => function ($q) {
                    $q->limit(1)
                        ->orderBy('is_default', 'desc');
                }
            ])
                ->orderBy('created_at', 'desc')
                ->when($request->search, fn($q) => $q->where('name', 'like', '%' . $request->search . '%'))
                ->when($request->status, fn($q) => $q->where('status', $request->status))
                ->when($request->with_trashed === true, fn($q) => $q->withTrashed())
                ->paginate(10)
                ->withQueryString(),
            'categories' => ProductCategory::select('id', 'name')->get(),
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name'         => ['required', 'string', 'max:60', 'min:3'],
            'sku'          => ['required', 'string', 'max:60', 'min:3'],
            'price'        => ['required', 'numeric', 'min:0'],
            'price_member' => ['required', 'numeric', 'min:0'],
            'stock'        => ['required', 'numeric', 'min:0'],
            'description'  => ['nullable', 'string', 'max:255', 'min:3'],
            'category_id'  => ['nullable', 'string',],
        ]);

        if (isset($request->category_id)) {
            ProductCategory::findOrFail($request->category_id);
        }

        // check if sku already exists
        $skuExists = Product::where('sku', $request->sku)->exists();

        if ($skuExists) {
            throw ValidationException::withMessages([
                'sku' => 'The sku has already been taken.',
            ]);
        }

        $newProduct = Product::create([
            'name'         => $request->name,
            'sku'          => $request->sku,
            'price'        => $request->price,
            'price_member' => $request->price_member,
            'stock'        => $request->stock,
            'description'  => $request->description,
            'category_id'  => $request->category_id,
        ]);

        // create 1 variant
        // ProductVariant::create([
        //     'name'         => null,
        //     'product_id'   => $newProduct->id,
        //     'sku'          => $newProduct->sku,
        //     'price'        => $newProduct->price,
        //     'price_member' => $newProduct->price_member,
        //     'stock'        => $newProduct->stock,
        // ]);

        // shippers
        $shippers = Shipper::whereNot('code', 'fonta')
            ->select('id')
            ->get();

        $newProduct->shippers()->attach($shippers->pluck('id')->toArray());

        return redirect()->back()->with('success', 'Product created successfully.');
    }

    public function edit($productId)
    {
        return inertia('Admin/Product/Edit', [
            'product' => Product::findOrFail($productId),
            'categories' => ProductCategory::select('id', 'name')->get(),
        ]);
    }

    public function update(Request $request, $productId)
    {
        $request->validate([
            'name'         => ['required', 'string', 'max:60', 'min:3'],
            'sku'          => ['nullable', 'string', 'max:60', 'min:3'],
            'status'       => ['nullable', 'string', 'in:published,draft'],
            'price'        => ['required', 'numeric', 'min:0'],
            'price_member' => ['required', 'numeric', 'min:0'],
            'stock'        => ['required', 'numeric', 'min:0'],
            'description'  => ['nullable', 'string', 'min:3'],
            'category_id'  => ['nullable', 'string',],
        ]);

        $product = Product::with(['images', 'shipping'])
            ->findOrFail($productId);

        if ($request->status === 'published') {
            // check media
            if ($product->images->count() === 0) {
                throw ValidationException::withMessages([
                    'message' => 'Product must have at least 1 media.',
                ]);
            }

            if (!$product->shipping) {
                throw ValidationException::withMessages([
                    'message' => 'Product must have shipping info.',
                ]);
            }

            $requiredShippingFields = ['weight', 'length', 'width', 'height'];

            foreach ($requiredShippingFields as $field) {
                if ($product->shipping->$field === null) {
                    throw ValidationException::withMessages([
                        'message' => "Product shipping info must include {$field}."
                    ]);
                }
            }

            // check shippers
            if ($product->shippers->count() === 0) {
                throw ValidationException::withMessages([
                    'message' => 'Product must have at least 1 shipper.',
                ]);
            }
        }


        if (isset($request->category_id)) {
            ProductCategory::findOrFail($request->category_id);
        }

        DB::transaction(function () use ($request, $product) {
            // check if sku already exists
            if ($request->sku && $request->sku !== $product->sku) {
                $skuExists = Product::where('sku', $request->sku)->exists();

                if ($skuExists) {
                    throw ValidationException::withMessages([
                        'sku' => 'The sku has already been taken.',
                    ]);
                }
            }

            $product->update([
                'name'         => $request->name ?? $product->name,
                'sku'          => $request->sku ?? $product->sku,
                'status'       => $request->status ?? $product->status,
                'price'        => $request->price ?? $product->price,
                'price_member' => $request->price_member ?? $product->price_member,
                'stock'        => $request->stock ?? $product->stock,
                'description'  => $request->description ?? $product->description,
                'category_id'  => $request->category_id ?? $product->category_id,
            ]);
        });

        return redirect()->back()->with('success', 'Product updated successfully.');
    }

    public function media($productId)
    {
        return inertia('Admin/Product/Media', [
            'product' => Product::with(['images' => function ($q) {
                $q->orderBy('is_default', 'desc')
                    ->orderBy('created_at', 'desc');
            }])
                ->findOrFail($productId),
        ]);
    }

    public function updateMedia(Request $request, $productId)
    {
        $request->validate([
            'is_default' => ['required', 'boolean'],
            'image'      => ['required', 'image', 'max:10048', 'mimes:jpeg,png,jpg'],
        ]);

        $product = Product::findOrFail($productId);

        $filePath = uploadFile($request->file('image'), 'products');

        if (!$filePath) {
            throw ValidationException::withMessages([
                'image' => 'Failed to upload image',
            ]);
        }

        $isDefault = $request->is_default;
        $hasDefaultImage = ProductImage::where('product_id', $product->id)
            ->where('is_default', true)->exists();

        if (!$hasDefaultImage) {
            $isDefault = 1;
        }

        ProductImage::create([
            'product_id' => $product->id,
            'image_path' => $filePath,
            'is_default' => $isDefault,
        ]);

        return redirect()->back()->with('success', 'Media uploaded successfully.');
    }

    public function deleteImage($imageId)
    {
        $image = ProductImage::findOrFail($imageId);

        // check if the image is default
        if ($image->is_default) {
            throw ValidationException::withMessages([
                'is_default' => 'Cannot delete default image.',
            ]);
        }

        $image->delete();

        return redirect()->back()->with('success', 'Image deleted successfully.');
    }

    public function setDefaultImage($productId, $imageId)
    {
        // set all image to false
        ProductImage::where('product_id', $productId)
            ->update([
                'is_default' => false,
            ]);

        $image = ProductImage::findOrFail($imageId);

        $image->update([
            'is_default' => true,
        ]);

        return redirect()->back()->with('success', 'Image set as default successfully.');
    }

    public function show($productId)
    {
        $product = Product::with([
            'variants.attributes.option',
            'images' => function ($query) {
                $query->select('id', 'product_id', 'image_path');
            },
            'shipping',
            'options.values',
        ])
            ->select('id', 'sku', 'name', 'description', 'price', 'price_member', 'stock')
            // ->published()
            ->findOrFail($productId);

        // default option
        $defaultOption = $product->options->map(function ($option) {
            return [
                'name'        => $option->name,
                'values'      => $option->values->first()->name,
            ];
        });

        return inertia('Admin/Product/Show', [
            'product' => $product,
            'defaultOption' => $defaultOption,
        ]);
    }

    public function destroy($productId)
    {
        $product = Product::findOrFail($productId);

        $product->delete();

        return redirect()->back()->with('success', 'Product deleted successfully.');
    }
}
