<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductShipping;
use Illuminate\Http\Request;

class ShippingController extends Controller
{
    public function index($productId)
    {
        $product = Product::with([
            'shipping',
        ])->findOrFail($productId);

        return inertia('Admin/Product/Shipping', [
            'product' => $product,
        ]);
    }

    public function updateShipping(Request $request, $productId)
    {
        $request->validate([
            'weight' => ['required', 'numeric', 'min:0'],
            'height' => ['required', 'numeric', 'min:1'],
            'length' => ['required', 'numeric', 'min:1'],
            'width'  => ['required', 'numeric', 'min:1'],
        ]);

        $product = Product::findOrFail($productId);

        ProductShipping::updateOrCreate([
            'product_id' => $product->id,
        ], [
            'weight' => $request->weight,
            'length' => $request->length,
            'width'  => $request->width,
            'height' => $request->height,
        ]);

        return redirect()->back()->with('success', 'Shipping information has been updated');
    }
}
