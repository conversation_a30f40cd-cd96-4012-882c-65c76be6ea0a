<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\MassDrop;
use App\Models\MassDropUser;
use App\Models\Order;
use App\Models\Product;
use App\Models\UserBalance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class MassDropController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Admin/MassDrop/Index', [
            'massDrops' => MassDrop::with([
                'product.images' => function ($query) {
                    $query->limit(1)
                        ->orderBy('is_default', 'desc');
                },
                'priceTiers',
                'users'
            ])
                ->orderBy('start_date', 'asc')
                ->orderBy('created_at', 'desc')
                ->paginate(10),
        ]);
    }

    public function create(Request $request)
    {
        return inertia('Admin/MassDrop/Create', [
            'products' => Product::select('id', 'name')
                // ->where('status', Product::STATUS_PUBLISH)
                ->orderBy('created_at', 'desc')
                ->get(),
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'image'                 => ['required', 'image', 'max:10048'],
            'name'                  => ['nullable', 'string'],
            'description'           => ['nullable', 'string'],
            'product_id'            => ['required', 'string'],
            'variant_id'            => ['nullable', 'string'],
            'start_date'            => ['required', 'date:Y-m-d H:i'],
            'end_date'              => ['required', 'date:Y-m-d H:i', 'after:start_date'],
            'min_order'             => ['nullable', 'integer', 'min:1'],
            'max_order'             => ['required', 'integer', 'min:1'],
            'status'                => ['required', 'in:draft,published'],
            'price_tier'            => ['required', 'array'],
            'price_tier.*.quantity' => ['required', 'integer', 'min:1'],
            'price_tier.*.price'    => ['required', 'numeric', 'min:1'],
        ]);

        // validate price tier
        $priceTiers = collect($request->price_tier);

        // price tier at least 2
        if ($priceTiers->count() < 2) {
            throw ValidationException::withMessages([
                'price_tier' => 'Price tier must be at least 2',
            ]);
        }

        $priceCurrent = $priceTiers[0]['price'];
        $quantityCurrent = $priceTiers[0]['quantity'];
        $loop = 0;

        foreach ($priceTiers as $priceTier) {

            if ($loop == 0) {
                $loop++;
                continue;
            }

            if ($priceTier['price'] >= $priceCurrent) {
                throw ValidationException::withMessages([
                    'message' => 'Price tier must be less than the previous price',
                ]);
            }

            if ($priceTier['quantity'] <= $quantityCurrent) {
                throw ValidationException::withMessages([
                    'message' => 'Quantity tier must be greater than the previous quantity',
                ]);
            }

            $priceCurrent = $priceTier['price'];
            $quantityCurrent = $priceTier['quantity'];

            $loop++;
        }

        $filePath = uploadFile($request->file('image'), 'mass-drop');

        if (!$filePath) {
            throw ValidationException::withMessages([
                'image' => 'Failed to upload image',
            ]);
        }

        $newMassDrop = MassDrop::create([
            'product_id'  => $request->product_id,
            'variant_id'  => $request->variant_id,
            'start_date'  => $request->start_date,
            'end_date'    => $request->end_date,
            'min_order'   => $request->min_order,
            'max_order'   => $request->max_order,
            'status'      => $request->status,
            'name'        => $request->name,
            'description' => $request->description,
            'image_path'  => $filePath,
        ]);

        $priceTiers->each(function ($priceTier, $index) use ($newMassDrop) {
            $newMassDrop->priceTiers()->create([
                'quantity' => $priceTier['quantity'],
                'price'    => $priceTier['price'],
                'order'    => $index,
            ]);
        });

        return redirect()->route('v1.admin.massDrop')->with('success', 'Mass drop created successfully');
    }

    public function show($massDropId)
    {
        $massDrop = MassDrop::with(['product', 'priceTiers', 'users'])
            ->findOrFail($massDropId);

        return inertia('Admin/MassDrop/Show', [
            'massDrop' => $massDrop,
        ]);
    }

    public function edit($massDropId)
    {
        $massDrop = MassDrop::with(['product', 'priceTiers', 'users'])
            ->findOrFail($massDropId);

        return inertia('Admin/MassDrop/Edit', [
            'massDrop' => $massDrop,
            'products' => Product::select('id', 'name')
                // ->where('status', Product::STATUS_PUBLISH)
                ->orderBy('created_at', 'desc')
                ->get(),
        ]);
    }

    public function update(Request $request, $massDropId)
    {
        $request->validate([
            'image'                 => ['nullable', 'image', 'max:10048'],
            'name'                  => ['nullable', 'string'],
            'description'           => ['nullable', 'string'],
            'product_id'            => ['required', 'string'],
            'variant_id'            => ['nullable', 'string'],
            'start_date'            => ['required', 'date:Y-m-d H:i'],
            'end_date'              => ['required', 'date:Y-m-d H:i', 'after:start_date'],
            'min_order'             => ['nullable', 'integer', 'min:1'],
            'max_order'             => ['required', 'integer', 'min:1'],
            'status'                => ['required', 'in:draft,published'],
            'price_tier'            => ['required', 'array'],
            'price_tier.*.quantity' => ['required', 'integer', 'min:1'],
            'price_tier.*.price'    => ['required', 'numeric', 'min:1'],
        ]);

        $massDrop = MassDrop::findOrFail($massDropId);

        // validate price tier
        $priceTiers = collect($request->price_tier);

        // price tier at least 2
        if ($priceTiers->count() < 2) {
            throw ValidationException::withMessages([
                'price_tier' => 'Price tier must be at least 2',
            ]);
        }

        $priceCurrent = $priceTiers[0]['price'];
        $quantityCurrent = $priceTiers[0]['quantity'];
        $loop = 0;

        foreach ($priceTiers as $priceTier) {

            if ($loop == 0) {
                $loop++;
                continue;
            }

            if ($priceTier['price'] >= $priceCurrent) {
                throw ValidationException::withMessages([
                    'message' => 'Price tier must be less than the previous price',
                ]);
            }

            if ($priceTier['quantity'] <= $quantityCurrent) {
                throw ValidationException::withMessages([
                    'message' => 'Quantity tier must be greater than the previous quantity',
                ]);
            }

            $priceCurrent = $priceTier['price'];
            $quantityCurrent = $priceTier['quantity'];

            $loop++;
        }

        if (isset($request->image)) {
            $massDrop->update([
                'image_path' => uploadFile($request->file('image'), 'mass-drop'),
            ]);
        }

        $massDrop->update([
            'product_id'  => $request->product_id ?? $massDrop->product_id,
            'variant_id'  => $request->variant_id ?? $massDrop->variant_id,
            'start_date'  => $request->start_date ?? $massDrop->start_date,
            'end_date'    => $request->end_date ?? $massDrop->end_date,
            'min_order'   => $request->min_order ?? $massDrop->min_order,
            'max_order'   => $request->max_order ?? $massDrop->max_order,
            'status'      => $request->status ?? $massDrop->status,
            'name'        => $request->name ?? $massDrop->name,
            'description' => $request->description ?? $massDrop->description,
        ]);

        $massDrop->priceTiers()->delete();

        $priceTiers->each(function ($priceTier, $index) use ($massDrop) {
            $massDrop->priceTiers()->create([
                'quantity' => $priceTier['quantity'],
                'price'    => $priceTier['price'],
                'order'    => $index,
            ]);
        });

        return redirect()->route('v1.admin.massDrop')->with('success', 'Mass drop updated successfully');
    }

    public function end($massDropId)
    {
        $massDrop = MassDrop::findOrFail($massDropId);

        if (!in_array($massDrop->status, [
            MassDrop::STATUS_PUBLISHED
        ])) {
            throw ValidationException::withMessages([
                'message' => 'Mass drop status must be published',
            ]);
        }

        // check if mass Drop has ended by date
        if ($massDrop->end_date > now()) {
            throw ValidationException::withMessages([
                'message' => 'Mass drop has not ended yet',
            ]);
        }

        $massDropUsers = MassDropUser::where('mass_drop_id', $massDropId)
            ->whereNull('deleted_at')
            ->get();

        try {
            DB::beginTransaction();

            $massDrop->update([
                'status' => MassDrop::STATUS_ENDED,
            ]);

            $tiers = $massDrop->priceTiers->sortByDesc('quantity');
            $baseTier = $tiers->last();

            $currentQuantity = $massDrop->count_orders;

            $finishedTier = null;

            foreach ($tiers as $tier) {
                if ($currentQuantity >= $tier->quantity) {
                    $finishedTier = $tier;

                    break;
                }
            }

            $cashback = floor(($baseTier->price - $finishedTier->price));

            $massDropUsers->each(function ($massDropUser) use ($cashback) {
                $cashbackUser = $cashback * $massDropUser->quantity;

                $userBalance = UserBalance::where('user_id', $massDropUser->user_id)
                    ->first();

                $order = Order::where('mass_drop_id', $massDropUser->mass_drop_id)
                    ->where('mass_drop_user_id', $massDropUser->id)
                    ->first();

                if (!$order) {
                    throw new \Exception('Order not found');
                }

                $newCoin = $userBalance->coin + $cashbackUser;

                $userBalance->logs()->create([
                    'user_id'       => $massDropUser->user_id,
                    'amount'        => $cashbackUser,
                    'new_amount'    => $newCoin,
                    'old_amount'    => $userBalance->coin,
                    'type'          => 'in',
                    'balance_type'  => 'coin',
                    'loggable_id'   => $order->id,
                    'loggable_type' => Order::class,
                ]);

                $userBalance->update([
                    'coin' => $userBalance->coin + $cashbackUser,
                ]);
            });

            DB::commit();

            return redirect()->route('v1.admin.massDrop')->with('success', 'Mass drop ended successfully');
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }
}
