<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $users = User::when($request->name, function ($query, $search) {
            return $query->where('name', 'like', "%$search%")
                ->orWhere('email', 'like', "%$search%");
        })
            ->select('id', 'name', 'email', 'created_at', 'mobile_phone')
            ->orderByDesc('created_at')
            ->paginate($request->per_page ?? 8);

        return response()->json($users);
    }
}
