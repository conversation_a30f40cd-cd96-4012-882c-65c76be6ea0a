<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class RefundController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Admin/Refund/Index', [
            'refunds' => Refund::with(['order.user', 'proofs'])
                ->when(isset($request->search), function ($query) use ($request) {
                    $query
                        ->where('id', 'like', '%' . $request->search . '%')
                        ->orWhereHas('order', function ($query) use ($request) {
                            $query->where('id', 'like', '%' . $request->search . '%');
                        });
                })
                ->when(isset($request->status), function ($query) use ($request) {
                    $query->where('status', $request->status);
                })
                ->when(isset($request->start_date), function ($query) use ($request) {
                    $query->whereDate('created_at', '>=', $request->start_date);
                })
                ->when(isset($request->end_date), function ($query) use ($request) {
                    $query->whereDate('created_at', '<=', $request->end_date);
                })
                ->orderBy('created_at', 'desc')
                ->paginate($request->per_page ?? 15)
        ]);
    }

    public function show($refundId)
    {
        return inertia('Admin/Refund/Show', [
            'refundData' => Refund::with([
                'order',
                'proofs',
                'order.user',
                'order.transactions',
            ])
                ->findOrFail($refundId)
        ]);
    }

    public function updateStatus(Request $request, $refundId)
    {
        $request->validate([
            'status' => ['required', 'in:approved,rejected']
        ]);

        $refund = Refund::with(['order'])
            ->findOrFail($refundId);

        if (! $refund) {
            throw new BadRequestHttpException('Refund not found.');
        }

        if ($request->status === Refund::STATUS_APPROVED) {
            $refund->order->update([
                'status' => Order::STATUS_REFUNDED
            ]);

            $refund->update([
                'status' => $request->status
            ]);

            activity(Order::EVENT_STATUS)
                ->causedBy(auth('web')->user())
                ->on($refund->order)
                ->event('updated')
                ->log('Refund ' . $request->status);

            return redirect()->back();
        } else {
            $refund->update([
                'status' => $request->status
            ]);

            activity(Order::EVENT_STATUS)
                ->causedBy(auth('web')->user())
                ->on($refund->order)
                ->event('updated')
                ->log('Refund ' . $request->status);
        }

        return redirect()->back();
    }
}
