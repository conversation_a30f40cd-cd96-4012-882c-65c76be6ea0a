<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Shipper;
use Illuminate\Http\Request;

class ShipperController extends Controller
{
    public function index($productId)
    {
        $product = Product::with([
            'shippers',
        ])->findOrFail($productId);

        $shippers = Shipper::all();

        $shipperWithStatus = $shippers->map(function ($shipper) use ($product) {
            $shipper->status = $product->shippers->contains($shipper->id);

            return $shipper;
        });

        return inertia('Admin/Product/Shipper', [
            'product' => $product,
            'shippers' => $shipperWithStatus,
        ]);
    }

    public function updateShipper(Request $request, $productId)
    {
        $request->validate([
            'shipper_ids' => ['required', 'array'],
            'shipper_ids.*' => ['exists:shippers,id'],
        ]);

        $product = Product::findOrFail($productId);

        $product->shippers()->sync($request->shipper_ids);

        return redirect()->route('v1.admin.products.shipper', $productId);
    }
}
