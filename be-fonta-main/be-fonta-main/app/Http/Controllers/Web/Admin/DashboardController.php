<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        // Total Revenue in the last 30 days with percentage change
        // total Products in the last 30 days with percentage change
        // total Orders in the last 30 days with percentage change
        // total Users in the last 30 days with percentage change

        $orders = Order::where('created_at', '>=', now()->subDays(30))
            ->whereIn('status', [
                Order::STATUS_PAID,
                Order::STATUS_ON_DELIVERY,
                Order::STATUS_DELIVERED,
                Order::STATUS_DONE
            ])
            ->count();
        $products = Product::where('created_at', '>=', now()->subDays(30))->count();
        $users = User::where('created_at', '>=', now()->subDays(30))->count();
        $revenue = Order::where('created_at', '>=', now()->subDays(30))
            ->whereIn('status', [
                Order::STATUS_PAID,
                Order::STATUS_ON_DELIVERY,
                Order::STATUS_DELIVERED,
                Order::STATUS_DONE
            ])
            ->sum('total');
        $previousOrders = Order::where('created_at', '<', now()->subDays(30))
            ->whereIn('status', [
                Order::STATUS_PAID,
                Order::STATUS_ON_DELIVERY,
                Order::STATUS_DELIVERED,
                Order::STATUS_DONE
            ])->count();
        $previousProducts = Product::where('created_at', '<', now()->subDays(30))->count();
        $previousUsers = User::where('created_at', '<', now()->subDays(30))->count();
        $previousRevenue = Order::where('created_at', '<', now()->subDays(30))
            ->whereIn('status', [
                Order::STATUS_PAID,
                Order::STATUS_ON_DELIVERY,
                Order::STATUS_DELIVERED,
                Order::STATUS_DONE
            ])
            ->sum('total');

        $ordersChange = $previousOrders ? (($orders - $previousOrders) / $previousOrders) * 100 : 0;
        $productsChange = $previousProducts ? (($products - $previousProducts) / $previousProducts) * 100 : 0;
        $usersChange = $previousUsers ? (($users - $previousUsers) / $previousUsers) * 100 : 0;
        $revenueChange = $previousRevenue ? (($revenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        return inertia('Admin/Dashboard/Index', [
            'summaries' => [
                'orders' => $orders,
                'products' => $products,
                'users' => $users,
                'revenue' => $revenue,
                'ordersChange' => $ordersChange,
                'productsChange' => $productsChange,
                'usersChange' => $usersChange,
                'revenueChange' => $revenueChange,
            ]
        ]);
    }
}
