<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    public function index()
    {
        return inertia('Admin/Faq/Index', [
            'faqs' => Faq::orderBy('order')->paginate(),
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'question' => ["required", "string", "max:255"],
            'answer' => ["required", "string"],
            'order' => ['required', 'integer'],
        ]);

        Faq::create($request->only('question', 'answer', 'order'));

        return redirect()->back()->with('success', 'FAQ created successfully.');
    }

    public function update(Request $request, $faqId)
    {
        $request->validate([
            'question' => ["required", "string", "max:255"],
            'answer' => ["required", "string"],
            'order' => ['required', 'integer'],
        ]);

        $faq = Faq::findOrFail($faqId);


        $faq->update([
            'question' => $request->input('question'),
            'answer' => $request->input('answer'),
            'order' => $request->input('order'),
        ]);

        return redirect()->back()->with('success', 'FAQ updated successfully.');
    }

    public function destroy($faqId)
    {
        $faq = Faq::findOrFail($faqId);
        $faq->delete();

        return redirect()->back()->with('success', 'FAQ deleted successfully.');
    }
}
