<?php

namespace App\Http\Controllers\Web\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Admin/Activity/Index', [
            'activities' => \Spatie\Activitylog\Models\Activity::with('causer')
                ->orderBy('created_at', 'desc')
                ->paginate(10),
        ]);
    }

    public function show(Request $request, $activityId)
    {
        return inertia('Admin/Activity/Show', [
            'activity' => \Spatie\Activitylog\Models\Activity::with('causer')
                ->findOrFail($activityId),
        ]);
    }
}
