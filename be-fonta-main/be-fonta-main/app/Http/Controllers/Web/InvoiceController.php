<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Number;
use Spatie\Browsershot\Browsershot;
use Spa<PERSON>\LaravelPdf\Facades\Pdf;

class InvoiceController extends Controller
{
    public function index(Request $request)
    {
        $invoice = $this->getOrder($request->only('id')['id']);

        if (! $invoice) {
            abort(404);
        }

        $params = [
            'invoice_number' => $invoice->invoice_number,
            'buyer_name'     => $invoice->user->name ?? '',
            'buyer_date'     => $invoice->created_at->format('d F Y'),
            'buyer_address'  => $invoice->address->address ?? '',
            'items'          => $invoice->orderItems->map(function ($item) {
                return [
                    'name'     => $item->product->name,
                    'quantity' => $item->quantity,
                    'price'    => Number::currency($item->price, 'IDR', 'id_ID'),
                    'total'    => Number::currency($item->price * $item->quantity, 'IDR', 'id_ID'),
                ];
            }),
            'shipping_cost'  => Number::currency($invoice->shipping_cost, 'IDR', 'id_ID'),
            'discount'       => Number::currency($invoice->discount_total, 'IDR', 'id_ID'),
            'sub_total'      => Number::currency($invoice->sub_total, 'IDR', 'id_ID'),
            'total'          => Number::currency($invoice->total, 'IDR', 'id_ID'),
            'payment_method' => $invoice->transactions->first()->payment_type ?? '',
            'last_update'    => $invoice->updated_at->format('d F Y H:i:s'),
            'status'         => $invoice->status,
        ];

        return view('pdf.invoice', $params);
    }

    // public function generateInvoice(Request $request)
    // {
    //     $invoice = $this->getOrder($request->only('id')['id']);

    //     $params = [
    //         'invoice_number' => $invoice->invoice_number,
    //         'buyer_name'     => $invoice->user->name ?? '',
    //         'buyer_date'     => $invoice->created_at->format('d F Y'),
    //         'buyer_address'  => $invoice->address->address ?? '',
    //         'items'          => $invoice->orderItems->map(function ($item) {
    //             return [
    //                 'name'     => $item->product->name,
    //                 'quantity' => $item->quantity,
    //                 'price'    => Number::currency($item->price, 'IDR', 'id_ID'),
    //                 'total'    => Number::currency($item->price * $item->quantity, 'IDR', 'id_ID'),
    //             ];
    //         }),
    //         'shipping_cost' => Number::currency($invoice->shipping_cost, 'IDR', 'id_ID'),
    //         'discount'      => Number::currency($invoice->discount_total, 'IDR', 'id_ID'),
    //         'sub_total'     => Number::currency($invoice->sub_total, 'IDR', 'id_ID'),
    //         'total'         => Number::currency($invoice->total, 'IDR', 'id_ID'),
    //         'payment_method' => $invoice->transactions->first()->payment_type ?? '',
    //         'last_update'   => $invoice->updated_at->format('d F Y H:i:s'),
    //         'status'        => $invoice->status,
    //     ];

    //     if (!$invoice) {
    //         abort(404);
    //     }

    //     return Pdf::view('pdf.invoice', $params)
    //         ->name('invoice-' . $invoice->invoice_number . '.pdf')
    //         ->withBrowsershot(function (Browsershot $browsershot) {
    //             $browsershot
    //                 ->setNodeBinary(config('option.path.node_binary'))
    //                 ->setNpmBinary(config('option.path.npm_binary'));
    //         })
    //         ->download();
    // }

    private function getOrder($invoiceNumber)
    {
        return Order::with(['orderItems.product', 'user', 'address', 'transactions' => function ($query) {
            // $query->where('status', Transaction::STATUS_PAID)
            $query->orderBy('created_at', 'desc')
                ->limit(1);
        }])
            ->where('invoice_number', $invoiceNumber)
            ->where('invoice_number', '!=', null)
            ->first();
    }
}
