<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class TestController extends Controller
{
    public function invoice()
    {
        $params = [
            'invoice_number' => 'INV/202108/001',
            'buyer_name'     => '<PERSON>',
            'buyer_date'     => '2021-08-01',
            'buyer_address'  => '123 Main St, Jakarta, Indonesia',
            'items'          => [
                [
                    'name'     => 'Product 1',
                    'quantity' => 2,
                    'price'    => 250000,
                    'total'    => 500000,
                ]
            ],
            'shipping_cost'  => 10000,
            'discount'       => 10000,
            'sub_total'      => 10000,
            'tax_total'      => 10000,
            'total'          => 10000,
            'payment_method' => 'Credit Card',
            'last_update'    => '2021-08-01 12:00:00',
            'status'         => 'Paid',
        ];

        return view('pdf.invoice', $params);
    }

    public function address()
    {
        return view('pdf.address', [
            'orders' => [
                [
                    'code' => 'INV/202108/001',
                    'date' => '2021-08-01',
                    'total' => 1000000,
                    'items' => [
                        [
                            'name' => 'Product 1',
                            'quantity' => 2,
                            'price' => 250000
                        ],
                        [
                            'name' => 'Product 2',
                            'quantity' => 1,
                            'price' => 500000
                        ],
                    ],
                    'sender' => [
                        'name' => 'Nama Pengirim',
                        'phone' => '08123456789',
                        'address' => 'Jalan Sender No. 123',
                        'city' => 'Jakarta',
                        'province' => 'DKI Jakarta',
                        'postal_code' => '12345'
                    ],
                    'recipient' => [
                        'name' => 'Nama Penerima',
                        'phone' => '08987654321',
                        'address' => 'Jalan Recipient No. 456',
                        'city' => 'Surabaya',
                        'province' => 'Jawa Timur',
                        'postal_code' => '67890',
                        'note' => 'Hubungi sebelum pengiriman'
                    ],
                ],
                [

                    'code' => 'INV/202108/001',
                    'date' => '2021-08-01',
                    'total' => 1000000,
                    'items' => [
                        [
                            'name' => 'Product 1',
                            'quantity' => 2,
                            'price' => 250000
                        ],
                        [
                            'name' => 'Product 2',
                            'quantity' => 1,
                            'price' => 500000
                        ],
                    ],
                    'sender' => [
                        'name' => 'Nama Pengirim',
                        'phone' => '08123456789',
                        'address' => 'Jalan Sender No. 123',
                        'city' => 'Jakarta',
                        'province' => 'DKI Jakarta',
                        'postal_code' => '12345'
                    ],
                    'recipient' => [
                        'name' => 'Nama Penerima',
                        'phone' => '08987654321',
                        'address' => 'Jalan Recipient No. 456',
                        'city' => 'Surabaya',
                        'province' => 'Jawa Timur',
                        'postal_code' => '67890',
                        'note' => 'Hubungi sebelum pengiriman'
                    ],
                ]
            ],
        ]);
    }
}
