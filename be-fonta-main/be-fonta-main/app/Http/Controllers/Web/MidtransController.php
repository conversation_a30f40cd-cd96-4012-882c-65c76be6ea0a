<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Services\PaymentService;
use Illuminate\Http\Request;

class MidtransController extends Controller
{
    private $paymentService;

    public function __construct(
        PaymentService $paymentService
    ) {
        $this->paymentService = $paymentService;
    }

    public function index(Request $request)
    {
        info('MidtransController.index');
        info($request->all());

        $this->paymentService->paymentCallback($request->all());
    }
}
