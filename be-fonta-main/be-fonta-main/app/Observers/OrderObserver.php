<?php

namespace App\Observers;

use App\Events\RefundOrderEvent;
use App\Jobs\MakeOrderDoneJob;
use App\Jobs\OrderToPointJob;
use App\Jobs\ReminderPaymentJob;
use App\Models\Order;

class OrderObserver
{
    /**
     * Handle the Order "created" event.
     */
    public function created(Order $order): void
    {
        activity(Order::EVENT_STATUS)
            ->causedBy(auth('web')->user() ?? auth('api')->user())
            ->on($order)
            ->event('created')
            ->withProperties([
                'attributes' => [
                    'status' => $order->status
                ]
            ])
            ->log('Order created');

        // reminder payment
        ReminderPaymentJob::dispatch($order)->delay(now()->addMinutes(5));
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $order): void
    {
        info('OrderObserver.updated');

        if ($order->getOriginal('status') !== $order->status) {
            activity(Order::EVENT_STATUS)
                ->causedBy(auth('web')->user() ?? auth('api')->user())
                ->performedOn($order)
                ->event('updated')
                ->withProperties([
                    'old' => [
                        'status' => $order->getOriginal('status')
                    ],
                    'attributes' => [
                        'status' => $order->status
                    ]
                ])
                ->log('Order status updated to ' . $order->status);
        }

        $newStatus = $order->status;

        if (in_array($newStatus, [Order::STATUS_REFUNDED])) {
            info('OrderObserver.updated: Refunded', [
                'order_id' => $order->id,
            ]);

            event(new RefundOrderEvent($order));
        }

        // UPDATE STATUS TO DONE, delay 2 day
        if (in_array($newStatus, [Order::STATUS_DELIVERED])) {
            // update status to done with job
            MakeOrderDoneJob::dispatch($order)
                ->delay(now()->addDays(2));
        }

        // add Point when order status DONE delay 2 day
        if (in_array($newStatus, [Order::STATUS_DONE])) {
            // add point
            OrderToPointJob::dispatch($order)
                ->delay(now()->addDays(2));
        }
    }

    /**
     * Handle the Order "deleted" event.
     */
    public function deleted(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "restored" event.
     */
    public function restored(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "force deleted" event.
     */
    public function forceDeleted(Order $order): void
    {
        //
    }
}
