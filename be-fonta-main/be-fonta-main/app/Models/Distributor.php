<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Distributor extends Model
{
    /** @use HasFactory<\Database\Factories\DistributorFactory> */
    use HasFactory, SoftDeletes, Timestamp, HasUuids;

    protected $fillable = [
        'name',
        'description',
        'address',
        'lat_long',
        'postal_code',
        'created_by',
        'pic_name',
        'pic_phone',
    ];
}
