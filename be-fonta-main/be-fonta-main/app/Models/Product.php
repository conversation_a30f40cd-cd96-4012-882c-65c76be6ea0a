<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;

class Product extends Model
{
    /** @use HasFactory<\Database\Factories\ProductFactory> */
    use HasFactory, SoftDeletes, Timestamp, HasUuids,  LogsActivity;

    const STATUS_PUBLISHED = 'published';
    const STATUS_DRAFT = 'draft';

    protected $fillable = [
        'name',
        'sku',
        'description',
        'status',
        'price',
        'price_member',
        'stock',
        'created_by',
        'category_id',
    ];

    protected $hidden = [
        'deleted_at',
    ];

    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id', 'id');
    }

    public function images()
    {
        return $this->hasMany(ProductImage::class);
    }

    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    public function options()
    {
        return $this->belongsToMany(ProductOption::class, 'product_product_options', 'product_id', 'product_option_id');
    }

    public function shipping()
    {
        return $this->hasOne(ProductShipping::class);
    }

    public function shippers()
    {
        return $this->belongsToMany(Shipper::class, 'product_shippers');
    }
}
