<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Bank extends Model
{
    use HasFactory, HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'name',
        'code_midtrans',
        'code_xendit',
    ];

    protected $hidden = [
        'deleted_at',
    ];
}
