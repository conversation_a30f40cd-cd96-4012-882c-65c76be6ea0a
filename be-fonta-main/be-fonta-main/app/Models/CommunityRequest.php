<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommunityRequest extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'community_id',
        'user_id',
        'status',
        'accepted_at',
        'rejected_at',
        'accepted_by',
        'rejected_by',
        'note',
    ];


    public function community()
    {
        return $this->belongsTo(Community::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
