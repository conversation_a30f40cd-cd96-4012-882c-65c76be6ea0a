<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductImage extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'product_id',
        'image_path',
        'is_default',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function imagePath(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $value ? assetPathStorage(config('filesystems.default')) . '/' . $value : null,
        );
    }
}
