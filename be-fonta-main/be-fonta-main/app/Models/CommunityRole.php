<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommunityRole extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'name',
        'role',
        'community_id',
    ];

    public function community()
    {
        return $this->belongsTo(Community::class);
    }
}
