<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Reminder extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    const TYPE_EMAIL = 'email';
    const TYPE_SMS = 'sms';
    const TYPE_PUSH = 'push';

    const STATUS_PENDING = 'pending';
    const STATUS_DONE = 'done';

    const REMINDER_INTERVAL_NONE = 'none';
    const REMINDER_INTERVAL_DAILY = 'daily';
    const REMINDER_INTERVAL_WEEKLY = 'weekly';
    const REMINDER_INTERVAL_MONTHLY = 'monthly';
    const REMINDER_INTERVAL_YEARLY = 'yearly';

    protected $fillable = [
        'title',
        'description',
        'type', // email, sms, push
        'status', // pending, done
        'reminder_at',
        'user_id',
        'reminder_interval', //none, daily, weekly, monthly, yearly
        'subject_id',
        'subject_type',
    ];

    protected $hidden = [
        'updated_at',
        'subject_type',
        'subject_id',
        'user_id',
        'deleted_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
