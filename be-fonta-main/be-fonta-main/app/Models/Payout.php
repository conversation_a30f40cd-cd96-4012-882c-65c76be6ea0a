<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payout extends Model
{
    use HasUuids, SoftDeletes, Timestamp, LogsActivity;

    const STATUS_PENDING = 'pending';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REJECTED = 'rejected';

    protected $fillable = [
        'bank_code',
        'bank_name',
        'account_number',
        'beneficiary_name',
        'description',
        'amount',
        'status',
        'type',
        'from',
        'reference',
        'user_id',
        'user_account_id',
    ];

    protected $hidden = [
        'deleted_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function userAccount()
    {
        return $this->belongsTo(UserAccount::class);
    }
}
