<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentMethod extends Model
{
    use Timestamp, SoftDeletes, LogsActivity;

    const FEE_TYPE_FIXED = 'fixed';
    const FEE_TYPE_PERCENTAGE = 'percentage';

    protected $fillable = [
        'name',
        'code',
        'provider', // midtrans, xendit, doku, etc
        'channel', // E-Wallet, Bank Transfer, Virtual Account, etc
        'icon_path',
        'description',
        'fee',
        'fee_type',
        'payment_instruction',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function iconPath(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $value ? assetPathStorage(config('filesystems.default')) . '/' . $value : null,
        );
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
