<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DiscountPurchasable extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'discount_id',
        'purchasable_id',
        'purchasable_type',
    ];

    public function discount()
    {
        return $this->belongsTo(Discount::class);
    }

    public function purchasable()
    {
        return $this->morphTo();
    }
}
