<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\CausesActivity;

class UserAddress extends Model
{
    use HasUuids, SoftDeletes, Timestamp, LogsActivity, CausesActivity;

    protected $fillable = [
        'user_id',
        'name',
        'address',
        'postal_code',
        'lat_lng',
        'is_default',
        'note',
        'name_receiver',
        'phone_receiver',
        'city_id',
        'province_id',
        'city',
        'type', // kota, kabupaten
        'province',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
