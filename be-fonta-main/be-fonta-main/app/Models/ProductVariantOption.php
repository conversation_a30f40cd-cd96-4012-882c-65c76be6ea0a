<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;

class ProductVariantOption extends Model
{
    use Timestamp;

    protected $table = 'product_variant_options';

    protected $fillable = [
        'product_variant_id',
        'product_option_variant_id',
    ];

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class);
    }

    public function optionVariant()
    {
        return $this->belongsTo(ProductOptionVariant::class, 'product_option_variant_id');
    }
}
