<?php

namespace App\Models;

use App\Observers\OrderObserver;
use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use \Spatie\Activitylog\Models\Activity;

#[ObservedBy([OrderObserver::class])]
class Order extends Model
{
    use HasUuids, SoftDeletes, Timestamp, LogsActivity;

    protected $keyType = 'string';

    const STATUS_PENDING = 'pending';
    const STATUS_PAID = 'paid';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_ON_DELIVERY = 'on delivery';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_DONE = 'done';
    const STATUS_REFUNDED = 'refunded';

    const EVENT_STATUS = 'order status';

    const CANCELLABLE_STATUSES = [
        self::STATUS_PENDING, // Allow to cancel with no changes
        self::STATUS_PAID, // Allow to cancel with return money by balance to user
    ];

    const REFUNDABLE_STATUSES = [
        self::STATUS_DELIVERED, // Allow to refund with return money by balance to user
        self::STATUS_DONE, // Allow to refund with return money by balance to user
    ];

    const COMPLETABLE_STATUSES = [
        self::STATUS_PAID,
        self::STATUS_ON_DELIVERY,
        self::STATUS_DELIVERED,
    ];

    protected $fillable = [
        'address_id',
        'distributor_id',
        'user_id',
        'status',
        'note',
        'mass_drop_id',
        'mass_drop_user_id',
        'notes',
        'total',
        'sub_total',
        'tax_total',
        'discount_total',
        'shipping_cost',
        'admin_fee',
        'invoice_number',
        'order_code',
    ];

    protected $hidden = [
        'deleted_at',
        'updated_at',
    ];

    public function distributor()
    {
        return $this->belongsTo(Distributor::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderDetail::class);
    }

    public function address()
    {
        return $this->belongsTo(UserAddress::class)
            ->withTrashed();
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function shipping()
    {
        return $this->hasOne(OrderShipping::class);
    }

    public function logs()
    {
        return $this->hasMany(Activity::class, 'subject_id')
            ->where('log_name', Order::EVENT_STATUS);
    }

    public static function getDefaultLogExcept(): array
    {
        return [
            'status',
        ];
    }
}
