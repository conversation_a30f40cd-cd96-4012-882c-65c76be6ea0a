<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Transaction extends Model
{
    use HasUuids, SoftDeletes, Timestamp, LogsActivity;

    const STATUS_PENDING = 'pending';
    const STATUS_PAID = 'paid';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_EXPIRED = 'expired';

    protected $fillable = [
        'user_id',
        'order_id',
        'order_code',
        'amount',
        'status',
        'note',
        'payment_type',
        'paid_at',
        'expired_at',
        'actions',
        'transaction_id', // transaction id Midtrans
        'response_json',
    ];

    protected $hidden = [
        'deleted_at',
        'response_json',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
