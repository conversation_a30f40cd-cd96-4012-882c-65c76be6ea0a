<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, HasRoles, HasUuids, LogsActivity, Notifiable, SoftDeletes, Timestamp;

    const IDENTITY_EMAIL = 'email';
    const IDENTITY_PHONE = 'phone';

    public static function getDefaultLogExcept(): array
    {
        return [
            'password',
            'pin',
            'remember_token',
        ];
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'mobile_phone',
        'is_whatsapp',
        'email_verified_at',
        'email_verification_token',
        'mobile_phone_verified_at',
        'mobile_phone_verification_token',
        'pin',
        'avatar_path',
        'password',
        'referral_code',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'email_verification_token',
        'mobile_phone_verification_token',
        'pin',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password'          => 'hashed',
        ];
    }

    // Rest omitted for brevity

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    // protected function mobilePhone(): Attribute
    // {
    //     return Attribute::make(
    //         get: fn($value) => substr($value, 2)
    //     );
    // }

    protected function email(): Attribute
    {
        return Attribute::make(
            set: fn($value) => strtolower($value)
        );
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            set: fn($value) => str()->title($value)
        );
    }

    public function isSuperAdmin(): bool
    {
        return $this->hasRole('super admin');
    }

    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    public function isDistributor(): bool
    {
        return $this->hasRole('distributor');
    }

    public function isCustomer(): bool
    {
        return $this->hasRole('customer');
    }

    public function avatarPath(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $value ? assetPathStorage(config('filesystems.default')) . '/' . $value : null,
        );
    }

    // Relasi untuk mendapatkan user yang direferensikan oleh user ini
    public function referrals()
    {
        return $this->hasMany(Referral::class, 'referred_by', 'id');
    }

    public function referBy()
    {
        return $this->hasOne(Referral::class, 'user_id', 'id');
    }

    public function referredBy()
    {
        return $this->belongsTo(User::class, 'referred_by', 'id');
    }
}
