<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MassDropPriceTier extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'mass_drop_id',
        'quantity',
        'price',
        'order',
    ];

    protected $hidden = [
        'updated_at',
        'deleted_at',
    ];

    public function massDrop()
    {
        return $this->belongsTo(MassDrop::class);
    }
}
