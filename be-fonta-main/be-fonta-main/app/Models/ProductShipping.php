<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductShipping extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'weight', // in gram
        'length', // in cm
        'width', // in cm
        'height', // in cm
        'product_id',
    ];

    protected $hidden = [
        'id',
        'product_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
