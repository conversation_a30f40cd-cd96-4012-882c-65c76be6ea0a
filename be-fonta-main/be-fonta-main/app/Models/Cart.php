<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Cart extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    const STATUS_PENDING = 'pending';
    const STATUS_CHECKOUT = 'checkout';

    protected $fillable = [
        'user_id',
        'status',
    ];

    protected $hidden = [
        'created_at',
        'deleted_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function cartItems()
    {
        return $this->hasMany(CartDetail::class);
    }
}
