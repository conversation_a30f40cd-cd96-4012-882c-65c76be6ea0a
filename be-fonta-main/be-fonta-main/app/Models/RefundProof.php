<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RefundProof extends Model
{
    use HasUuids, SoftDeletes, Timestamp, LogsActivity;

    protected $fillable = [
        'file_path',
        'file_name',
        'file_type',
        'file_size',
        'refund_id',
    ];

    public function refund()
    {
        return $this->belongsTo(Refund::class);
    }

    public function filePath(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $value ? assetPathStorage(config('filesystems.default'))  . '/' . $value : null,
        );
    }
}
