<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserBalanceLog extends Model
{
    use HasUuids, SoftDeletes, Timestamp, LogsActivity;

    protected $fillable = [
        'amount',
        'new_amount',
        'old_amount',
        'type',  // in, out
        'balance_type', // balance, coin, point
        'user_id',
        'user_balance_id',
        'loggable_id',
        'loggable_type',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function userBalance()
    {
        return $this->belongsTo(UserBalance::class);
    }

    public function loggable()
    {
        return $this->morphTo();
    }
}
