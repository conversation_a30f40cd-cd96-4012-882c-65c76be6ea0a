<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MassDrop extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    const STATUS_PUBLISHED = 'published';
    const STATUS_DRAFT = 'draft';
    const STATUS_ENDED = 'ended';

    protected $fillable = [
        'name',
        'description',
        'status',
        'start_date',
        'end_date',
        'min_order',
        'max_order',
        'product_id',
        'variant_id',
        'image_path',
        'count_users',
        'count_orders',
    ];

    protected $hidden = [
        'updated_at',
        'deleted_at',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'mass_drop_users')
            ->whereNull('mass_drop_users.deleted_at');
    }

    public function priceTiers()
    {
        return $this->hasMany(MassDropPriceTier::class);
    }

    public function basePrice()
    {
        return $this->hasOne(MassDropPriceTier::class)
            ->where('order', 0);
    }

    public function imagePath(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $value ? assetPathStorage(config('filesystems.default')) . '/' . $value : null,
        );
    }

    public function scopeActive($query)
    {
        // only show start date in 2 days
        // and end date is not passed
        return $query->where('status', self::STATUS_PUBLISHED)
            ->where('start_date', '<=', now())
            ->where('end_date', '>', now());
    }

    public function scopeComingSoon($query)
    {
        // only show start date in more than 2 days
        // and end date is not passed
        return $query->where('status', self::STATUS_PUBLISHED)
            ->where('start_date', '>=', now()->addDays(2))
            ->where('end_date', '>', now());
    }

    public function reminder()
    {
        return $this->morphMany(Reminder::class, 'subject');
    }
}
