<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductVariant extends Model
{
    use SoftDeletes, HasUuids, Timestamp, LogsActivity;

    protected $fillable = [
        'product_id',
        'sku',
        'price',
        'price_member',
        'stock',
        'name',
        'options',
    ];

    protected $hidden = [
        'deleted_at',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function attributes()
    {
        return $this->belongsToMany(
            ProductOptionVariant::class,
            'product_variant_options', // pivot table
            'product_variant_id', // foreign key on pivot table
            'product_option_variant_id', // foreign key on pivot table
        );
    }
}
