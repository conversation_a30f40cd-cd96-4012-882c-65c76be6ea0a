<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;

class ProductOptionVariant extends Model
{
    use Timestamp;

    protected $table = 'product_option_variants';

    protected $fillable = [
        'product_option_id',
        'name',
    ];

    public function option()
    {
        return $this->belongsTo(ProductOption::class, 'product_option_id');
    }
}
