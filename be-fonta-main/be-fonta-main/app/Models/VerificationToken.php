<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;

class VerificationToken extends Model
{
    use Timestamp;

    const VERIFY_EMAIL = 'verify email';
    const VERIFY_PHONE = 'verify phone';
    const VERIFY_OTP = 'verify otp';
    const PAYOUT = 'payout';

    protected $fillable = [
        'identity',
        'token',
        'purpose', // payout, verify email, verify phone, verify otp, etc
    ];
}
