<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommunityShow extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'community_id',
        'title',
        'description',
        'image_path',
        'video_path',
        'status',
        'published_at',
        'type',
        'location',
    ];

    const TYPE_EVENT = 'event';
    const TYPE_EDUCATION = 'education';

    public function community()
    {
        return $this->belongsTo(Community::class);
    }

    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }
}
