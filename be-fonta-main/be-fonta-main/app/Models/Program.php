<?php

namespace App\Models;

use App\Traits\LogsActivity;
use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Program extends Model
{
    use SoftDeletes, Timestamp, LogsActivity;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'started_at',
        'ended_at',
    ];

    public function users()
    {
        return $this->belongsToMany(User::class, 'program_users', 'program_id', 'user_id');
    }
}
