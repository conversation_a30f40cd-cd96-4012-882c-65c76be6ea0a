<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductShipper extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = ['product_id', 'shipper_id'];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function shipper()
    {
        return $this->belongsTo(Shipper::class);
    }
}
