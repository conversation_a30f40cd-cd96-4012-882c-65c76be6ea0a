<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserDevice extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    protected $fillable = [
        'user_id',
        'firebase_token',
        'device_id',
        'device_name',
        'device_type',
        'device_brand',
        'device_os',
        'device_os_version',
        'device_model',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeByFirebaseToken($query, $firebaseToken)
    {
        return $query->where('firebase_token', $firebaseToken);
    }

    public function scopeByDeviceId($query, $deviceId)
    {
        return $query->where('device_id', $deviceId);
    }
}
