<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductProductOption extends Model
{
    use HasUuids, Timestamp;

    protected $table = 'product_product_options';

    protected $fillable = [
        'product_id',
        'product_option_id',
        'position',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function productOption()
    {
        return $this->belongsTo(ProductOption::class);
    }
}
