<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ReferralUsage extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    const TYPE_REGISTER = 'register';
    const TYPE_PURCHASE = 'purchase';

    protected $fillable = [
        'referrer_id', // user_id, user who refers
        'used_by', // user_id, user who uses the referral
        'referral_code',
        'point', // points given to referrer
        'description',
        'type', // type of referral usage
    ];

    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    public function usedBy()
    {
        return $this->belongsTo(User::class, 'used_by');
    }
}
