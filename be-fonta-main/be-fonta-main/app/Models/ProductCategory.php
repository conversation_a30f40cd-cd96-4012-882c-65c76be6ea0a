<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductCategory extends Model
{
    use SoftDeletes, HasUuids, Timestamp;

    protected $keyType = 'string';

    protected $fillable = ['name'];

    protected $hidden = ['deleted_at', 'updated_at'];

    public function products()
    {
        return $this->hasMany(Product::class);
    }
}
