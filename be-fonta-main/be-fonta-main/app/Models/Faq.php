<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Faq extends Model
{
    protected $fillable = [
        'question',
        'answer',
        'category',
        'order',
    ];

    protected $casts = [
        'order' => 'integer',
    ];

    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrder($query, $order)
    {
        return $query->orderBy('order', $order);
    }
}
