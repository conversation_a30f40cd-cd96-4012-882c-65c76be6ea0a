<?php

namespace App\Models;

use Carbon\Traits\Timestamp;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MassDropUser extends Model
{
    use HasUuids, SoftDeletes, Timestamp;

    const STATUS_PENDING = 'pending';
    const STATUS_PAID = 'paid';
    const STATUS_CANCELLED = 'cancelled';

    protected $fillable = [
        'mass_drop_id',
        'user_id',
        'quantity',
        'status',
    ];

    protected $casts = [
        'quantity' => 'integer',
    ];

    public function massDrop()
    {
        return $this->belongsTo(MassDrop::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
