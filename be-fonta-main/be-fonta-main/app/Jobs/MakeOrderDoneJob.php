<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\UserBalance;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class MakeOrderDoneJob implements ShouldQueue
{
    use Queueable;

    protected $order;

    public function __construct(
        Order $order
    ) {
        $this->order = $order;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        info('MakeOrderDoneJob.handle');

        // check if order status delivered
        if ($this->order->status === Order::STATUS_DELIVERED) {
            // add point
            info('MakeOrderDoneJob.handle.updateOrderStatusToDone');

            $this->order->update([
                'status' => Order::STATUS_DONE
            ]);
        } else {
            info('MakeOrderDoneJob.handle.statusNotDelivered', [
                'order_id' => $this->order->id,
                'status' => $this->order->status,
            ]);
        }
    }
}
