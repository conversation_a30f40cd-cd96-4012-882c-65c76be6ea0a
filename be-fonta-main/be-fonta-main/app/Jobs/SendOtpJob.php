<?php

namespace App\Jobs;

use App\Mail\SendOtp;
use App\Services\Impl\VerifyImplService;
use App\Services\WhatsappService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class SendOtpJob implements ShouldQueue
{
    use Queueable;

    protected $type; // email or phone
    protected $identity; // email or phone number
    protected $params;
    protected $otp;

    public function __construct(
        string $type,
        string $identity,
        array $params,
        string $otp
    ) {
        $this->type = $type;
        $this->identity = $identity;
        $this->params = $params;
        $this->otp = $otp;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        info('SendOtpJob.handle');

        if ($this->type === 'email') {
            info('Send OTP to email ' . $this->identity);

            Mail::to($this->identity)->send(new SendOtp($this->params));
        } else {
            // Send OTP to phone
            info('Send OTP to phone ' . $this->identity);
            $this->params['otp'] = $this->otp;

            app(WhatsappService::class)->sendOtp($this->identity, $this->params);
        }
    }
}
