<?php

namespace App\Jobs;

use App\Enums\NotificationType;
use App\Enums\Screen;
use App\Events\NotificationEvent;
use App\Models\Order;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ReminderPaymentJob implements ShouldQueue
{
    use Queueable;

    protected $order;

    public function __construct(
        Order $order
    ) {
        $this->order = $order;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        info('ReminderPaymentJob.handle', [
            'order_id' => $this->order->id,
        ]);

        // check if order status pending
        if ($this->order->status === Order::STATUS_PENDING) {
            // send notification
            info('ReminderPaymentJob.handle.sendNotification', [
                'order_id' => $this->order->id,
            ]);

            event(new NotificationEvent(
                userId: $this->order->user_id,
                title: 'Pembayaran Diperlukan',
                body: 'Selesaikan pembayaran untuk pesanan dengan invoice ' . $this->order->invoice_number,
                data: [
                    'screen' => Screen::TRANSACTION_DETAIL->value,
                    'id'     => $this->order->id,
                ],
                type: NotificationType::INFO->value,
            ));

            return;
        }

        info('ReminderPaymentJob.handle.statusNotPending', [
            'order_id' => $this->order->id,
            'status' => $this->order->status,
        ]);
    }
}
