<?php

namespace App\Jobs;

use App\Services\Impl\VerifyImplService;
use App\Services\WhatsappService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class MobilePhoneVerificationJob implements ShouldQueue
{
    use Queueable;

    protected $to;
    protected $params;

    public function __construct($to, $params)
    {
        $this->to = $to;
        $this->params = $params;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->params['is_whatsapp'] === 1) {
            info('Send mobile phone verification to ' . $this->to . ' by WhatsApp');

            app(WhatsappService::class)->sendOtp($this->to, $this->params);

            return;
        }

        info('Send mobile phone verification to ' . $this->to . ' by SMS');
    }
}
