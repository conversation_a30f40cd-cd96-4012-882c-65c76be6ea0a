<?php

namespace App\Jobs;

use App\Mail\EmailVerification;
use App\Mail\SendOtp;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class EmailVerificationJob implements ShouldQueue
{
    use Queueable;

    protected $to;
    protected $params;

    public function __construct($to, $params)
    {
        $this->to = $to;
        $this->params = $params;
    }


    public function handle(): void
    {
        info('Send email verification to ' . $this->to);

        Mail::to($this->to)->send(new SendOtp($this->params));
    }
}
