<?php

namespace App\Jobs;

use App\Enums\NotificationType;
use App\Enums\Screen;
use App\Events\NotificationEvent;
use App\Models\Order;
use App\Models\UserBalance;
use App\Models\UserBalanceLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class OrderToPointJob implements ShouldQueue
{
    use Queueable;

    protected $order;

    public function __construct(
        Order $order
    ) {
        $this->order = $order;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        info('OrderToPointJob.handle', [
            'order_id' => $this->order->id,
        ]);

        // check if order status done
        if ($this->order->status === Order::STATUS_DONE) {
            // add point
            info('OrderToPointJob.handle.addPointFromOrder');

            $alreadyHasPoint = UserBalanceLog::where('loggable_id', $this->order->id)
                ->where('balance_type', 'point')
                ->where('type', 'in')
                ->exists();

            if ($alreadyHasPoint) {
                info('OrderToPointJob.handle.alreadyHasPoint', [
                    'order_id' => $this->order->id,
                ]);

                return;
            }

            $userBalance = UserBalance::where('user_id', $this->order->user_id)->first();

            if ($userBalance) {
                $newPoint = $userBalance->point + floor($this->order->sub_total / config('option.conversion_rate'));

                event(new NotificationEvent(
                    userId: $this->order->user_id,
                    title: 'Poin Diterima',
                    body: 'Selamat! Anda mendapatkan ' . (int) $newPoint . ' poin dari pesanan dengan invoice ' . $this->order->invoice_number,
                    data: [
                        'screen' => Screen::PROFILE->value,
                        'id'     => null,
                    ],
                    type: NotificationType::INFO->value,
                ));

                $userBalance->logs()->create([
                    'user_id'       => $this->order->user_id,
                    'amount'        => abs($newPoint - $userBalance->point),
                    'new_amount'    => $newPoint,
                    'old_amount'    => $userBalance->point,
                    'type'          => 'in',
                    'balance_type'  => 'point',
                    'loggable_id'   => $this->order->id,
                    'loggable_type' => Order::class,
                ]);

                $userBalance->update([
                    'point' => $newPoint,
                ]);
            }
        } else {
            info('OrderToPointJob.handle.statusNotDone', [
                'order_id' => $this->order->id,
                'status'   => $this->order->status,
            ]);
        }
    }
}
