<?php

namespace App\Jobs;

use App\Models\Order;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class AddPointJob implements ShouldQueue
{
    use Queueable;

    protected $order;

    public function __construct(
        Order $order
    ) {
        $this->order = $order;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        info('AddPointJob.handle');

        info([
            'order' => $this->order->toArray()
        ]);
    }
}
