<?php

namespace App\Jobs;

use App\Models\Reminder;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendR<PERSON>inderJob implements ShouldQueue
{
    use Queueable;

    public $reminder;

    public function __construct(
        Reminder $reminder
    ) {
        $this->reminder = $reminder;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        info('SendReminderJob.handle');

        if ($this->reminder->deleted_at) {
            info('SendReminderJob.handle: reminder deleted', [
                'reminder_id' => $this->reminder->id,
            ]);

            return;
        }

        app(NotificationService::class)->sendByUser(
            user: $this->reminder->user,
            title: $this->reminder->title,
            body: $this->reminder->description,
            data: [
                'screen' => 'massDropDetail',
                'id'     => $this->reminder->subject_id,
            ],
            type: 'massDrop',
        );

        Reminder::where('id', $this->reminder->id)->update([
            'status' => Reminder::STATUS_DONE,
        ]);
    }
}
