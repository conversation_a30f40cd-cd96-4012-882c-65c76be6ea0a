<?php

namespace App\Providers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Opcodes\LogViewer\Facades\LogViewer;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        /** @disregard */
        DB::prohibitDestructiveCommands(
            $this->app->isProduction()
        );

        if (!app()->environment('local')) {
            URL::forceScheme('https');

            LogViewer::auth(function ($request) {
                return true;
            });
        }
    }
}
