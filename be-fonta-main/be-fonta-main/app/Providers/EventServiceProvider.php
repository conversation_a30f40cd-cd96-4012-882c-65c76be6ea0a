<?php

namespace App\Providers;

use App\Events\CancelOrderEvent;
use App\Events\NotificationEvent;
use App\Events\PaymentEvent;
use App\Events\RefundOrderEvent;
use App\Events\UserRegisterEvent;
use App\Listeners\CancelMassDrop;
use App\Listeners\InitialBalance;
use App\Listeners\ReducePoint;
use App\Listeners\ReturnCoin;
use App\Listeners\SendEmailVerification;
use App\Listeners\SendNotification;
use App\Listeners\SendNotificationOrder;
use App\Listeners\SendPhoneVerification;
use App\Listeners\UpdateOrderStatus;
use Illuminate\Support\ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        CancelOrderEvent::class => [
            CancelMassDrop::class,
            ReturnCoin::class,
        ],

        PaymentEvent::class => [
            // Add listeners here
            UpdateOrderStatus::class,
            SendNotificationOrder::class,
        ],

        RefundOrderEvent::class => [
            // Add listeners here
            ReducePoint::class,
        ],

        UserRegisterEvent::class => [
            // Add listeners here
            InitialBalance::class,
            SendEmailVerification::class,
            SendPhoneVerification::class,
        ],

        NotificationEvent::class => [
            // Add listeners here
            SendNotification::class,
        ],
    ];



    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
